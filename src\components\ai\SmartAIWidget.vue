<template>
  <q-card class="smart-ai-widget" :class="{ 'compact': compact }">
    <!-- Header -->
    <q-card-section class="bg-primary text-white q-pa-sm">
      <div class="row items-center no-wrap">
        <q-icon name="psychology" size="sm" class="q-mr-xs" />
        <div class="text-subtitle2 text-weight-medium ellipsis">
          {{ widgetTitle }}
        </div>
        <q-space />
        <q-btn
          v-if="!alwaysExpanded"
          flat
          round
          dense
          size="xs"
          :icon="expanded ? 'expand_less' : 'expand_more'"
          @click="toggleExpanded"
          class="text-white"
        />
      </div>
    </q-card-section>

    <!-- Content -->
    <q-slide-transition>
      <div v-show="expanded || alwaysExpanded">
        <q-card-section class="q-pa-sm">
          <!-- Profile-aware suggestions -->
          <div v-if="profileSuggestions.length > 0" class="q-mb-sm">
            <div class="text-caption text-grey-7 q-mb-xs">For {{ profileTypeLabel }}:</div>
            <div class="row q-col-gutter-xs">
              <div 
                v-for="suggestion in profileSuggestions" 
                :key="suggestion.key"
                :class="compact ? 'col-12' : 'col-6'"
              >
                <q-btn
                  :color="suggestion.color"
                  :icon="suggestion.icon"
                  :label="suggestion.label"
                  size="xs"
                  outline
                  no-caps
                  class="full-width ai-suggestion-btn"
                  @click="handleAITrigger(suggestion.key)"
                >
                  <q-tooltip>{{ suggestion.tooltip }}</q-tooltip>
                </q-btn>
              </div>
            </div>
          </div>

          <!-- Quick actions -->
          <div v-if="quickActions.length > 0">
            <div class="text-caption text-grey-7 q-mb-xs">Quick Help:</div>
            <div class="row q-col-gutter-xs">
              <div 
                v-for="action in quickActions" 
                :key="action.key"
                :class="compact ? 'col-12' : 'col-6'"
              >
                <q-btn
                  :color="action.color"
                  :icon="action.icon"
                  :label="action.label"
                  size="xs"
                  flat
                  no-caps
                  class="full-width ai-action-btn"
                  @click="handleAITrigger(action.key)"
                >
                  <q-tooltip>{{ action.tooltip }}</q-tooltip>
                </q-btn>
              </div>
            </div>
          </div>

          <!-- Context-specific recommendations -->
          <div v-if="contextRecommendations.length > 0" class="q-mt-sm">
            <div class="text-caption text-grey-7 q-mb-xs">Recommended:</div>
            <q-list dense class="rounded-borders">
              <q-item 
                v-for="rec in contextRecommendations" 
                :key="rec.key"
                clickable
                v-ripple
                class="ai-recommendation-item"
                @click="handleAITrigger(rec.key)"
              >
                <q-item-section avatar>
                  <q-icon :name="rec.icon" :color="rec.color" size="xs" />
                </q-item-section>
                <q-item-section>
                  <q-item-label class="text-caption">{{ rec.label }}</q-item-label>
                </q-item-section>
                <q-item-section side>
                  <q-icon name="psychology" color="primary" size="xs" />
                </q-item-section>
              </q-item>
            </q-list>
          </div>
        </q-card-section>
      </div>
    </q-slide-transition>
  </q-card>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { useProfileStore } from '../../stores/profileStore';
import { useRoute } from 'vue-router';
import { useGlobalServicesStore } from '../../stores/globalServices';
import { formatProfileType } from '../../services/profileTypes';

interface AIAction {
  key: string;
  label: string;
  icon: string;
  color: string;
  tooltip: string;
}

const props = defineProps<{
  context?: 'dashboard' | 'profile' | 'community' | 'sidebar';
  compact?: boolean;
  alwaysExpanded?: boolean;
  maxSuggestions?: number;
}>();

const profileStore = useProfileStore();
const route = useRoute();
const globalServices = useGlobalServicesStore();
const expanded = ref(props.alwaysExpanded || false);

// Widget title based on context
const widgetTitle = computed(() => {
  switch (props.context) {
    case 'dashboard': return 'AI Dashboard Assistant';
    case 'profile': return 'Profile AI Helper';
    case 'community': return 'Community AI Guide';
    default: return 'AI Assistant';
  }
});

// Current profile type
const currentProfileType = computed(() => {
  return profileStore.currentProfile?.profile_type || null;
});

const profileTypeLabel = computed(() => {
  return currentProfileType.value ? formatProfileType(currentProfileType.value) : 'User';
});

// Profile-specific suggestions
const profileSuggestions = computed((): AIAction[] => {
  if (!currentProfileType.value) return [];

  const suggestions: Record<string, AIAction[]> = {
    innovator: [
      {
        key: 'find_investors',
        label: 'Find Investors',
        icon: 'attach_money',
        color: 'green',
        tooltip: 'Connect with potential investors'
      },
      {
        key: 'find_mentors',
        label: 'Find Mentors',
        icon: 'school',
        color: 'blue',
        tooltip: 'Connect with experienced mentors'
      }
    ],
    investor: [
      {
        key: 'discover_projects',
        label: 'Discover Projects',
        icon: 'lightbulb',
        color: 'orange',
        tooltip: 'Find promising projects to invest in'
      },
      {
        key: 'market_trends',
        label: 'Market Trends',
        icon: 'trending_up',
        color: 'purple',
        tooltip: 'Get market insights and trends'
      }
    ],
    mentor: [
      {
        key: 'mentorship_opportunities',
        label: 'Find Mentees',
        icon: 'people',
        color: 'teal',
        tooltip: 'Find innovators seeking mentorship'
      },
      {
        key: 'knowledge_sharing',
        label: 'Share Knowledge',
        icon: 'share',
        color: 'indigo',
        tooltip: 'Share your expertise with the community'
      }
    ]
  };

  const profileSugs = suggestions[currentProfileType.value] || [];
  return profileSugs.slice(0, props.maxSuggestions || 4);
});

// Quick actions available to all users
const quickActions = computed((): AIAction[] => {
  const actions: AIAction[] = [
    {
      key: 'find_matches',
      label: 'Find Matches',
      icon: 'people_alt',
      color: 'primary',
      tooltip: 'Find relevant connections'
    },
    {
      key: 'content_recommendations',
      label: 'Content Tips',
      icon: 'article',
      color: 'secondary',
      tooltip: 'Get content recommendations'
    }
  ];

  // Add context-specific actions
  if (props.context === 'community') {
    actions.push({
      key: 'content_ideas',
      label: 'Post Ideas',
      icon: 'create',
      color: 'accent',
      tooltip: 'Get ideas for your next post'
    });
  }

  return actions.slice(0, props.maxSuggestions || 4);
});

// Context-specific recommendations
const contextRecommendations = computed((): AIAction[] => {
  const currentPath = route.path;
  
  if (currentPath.includes('/profile')) {
    return [
      {
        key: 'complete_profile',
        label: 'Complete Profile',
        icon: 'assignment_turned_in',
        color: 'blue',
        tooltip: 'Get help completing your profile'
      },
      {
        key: 'profile_optimization',
        label: 'Optimize Profile',
        icon: 'tune',
        color: 'purple',
        tooltip: 'Optimize for better visibility'
      }
    ];
  }

  if (currentPath.includes('/community')) {
    return [
      {
        key: 'networking',
        label: 'Networking Tips',
        icon: 'group',
        color: 'teal',
        tooltip: 'Get networking advice'
      },
      {
        key: 'content_discovery',
        label: 'Discover Content',
        icon: 'explore',
        color: 'cyan',
        tooltip: 'Find relevant discussions'
      }
    ];
  }

  return [];
});

// Toggle expanded state
const toggleExpanded = () => {
  expanded.value = !expanded.value;
};

// Handle AI trigger clicks
const handleAITrigger = async (triggerKey: string) => {
  try {
    await globalServices.aiChatTriggerService.triggerChat(triggerKey, currentProfileType.value || undefined);
  } catch (error) {
    console.error('Error triggering AI chat:', error);
  }
};
</script>

<style scoped>
.smart-ai-widget {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.smart-ai-widget:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.smart-ai-widget.compact {
  max-width: 280px;
}

.ai-suggestion-btn {
  border-radius: 6px;
  font-size: 0.75rem;
  min-height: 28px;
}

.ai-action-btn {
  border-radius: 4px;
  font-size: 0.7rem;
  min-height: 24px;
}

.ai-recommendation-item {
  border-radius: 4px;
  margin-bottom: 2px;
}

.ai-recommendation-item:hover {
  background-color: rgba(0, 0, 0, 0.04);
}
</style>
