<template>
  <div class="unified-services-demo">
    <q-card class="q-ma-md">
      <q-card-section class="bg-primary text-white">
        <div class="text-h6">
          <q-icon name="speed" class="q-mr-sm" />
          Unified Services Demo
        </div>
        <div class="text-caption">
          Demonstrating unified caching and real-time services
        </div>
      </q-card-section>

      <q-card-section>
        <div class="row q-gutter-md">
          <!-- Cache Statistics -->
          <div class="col-12 col-md-6">
            <q-card flat bordered>
              <q-card-section>
                <div class="text-h6 q-mb-md">
                  <q-icon name="storage" class="q-mr-sm" />
                  Cache Statistics
                </div>
                
                <div class="q-gutter-sm">
                  <div class="row items-center">
                    <div class="col-6">Hit Rate:</div>
                    <div class="col-6">
                      <q-linear-progress 
                        :value="cacheStats.hitRate" 
                        color="positive"
                        class="q-mr-sm"
                      />
                      {{ (cacheStats.hitRate * 100).toFixed(1) }}%
                    </div>
                  </div>
                  
                  <div class="row">
                    <div class="col-6">Total Entries:</div>
                    <div class="col-6">{{ cacheStats.totalEntries }}</div>
                  </div>
                  
                  <div class="row">
                    <div class="col-6">Memory Usage:</div>
                    <div class="col-6">{{ (cacheStats.memoryUsage / 1024).toFixed(1) }} KB</div>
                  </div>
                  
                  <div class="row">
                    <div class="col-6">Cache Hits:</div>
                    <div class="col-6 text-positive">{{ cacheStats.hits }}</div>
                  </div>
                  
                  <div class="row">
                    <div class="col-6">Cache Misses:</div>
                    <div class="col-6 text-negative">{{ cacheStats.misses }}</div>
                  </div>
                </div>

                <div class="q-mt-md">
                  <q-btn 
                    @click="clearCache" 
                    color="warning" 
                    size="sm" 
                    label="Clear Cache"
                    icon="clear_all"
                  />
                  <q-btn 
                    @click="refreshStats" 
                    color="primary" 
                    size="sm" 
                    label="Refresh"
                    icon="refresh"
                    class="q-ml-sm"
                  />
                </div>
              </q-card-section>
            </q-card>
          </div>

          <!-- Real-time Statistics -->
          <div class="col-12 col-md-6">
            <q-card flat bordered>
              <q-card-section>
                <div class="text-h6 q-mb-md">
                  <q-icon name="wifi" class="q-mr-sm" />
                  Real-time Statistics
                </div>
                
                <div class="q-gutter-sm">
                  <div class="row items-center">
                    <div class="col-6">Connection:</div>
                    <div class="col-6">
                      <q-chip 
                        :color="connectionStateColor" 
                        text-color="white" 
                        size="sm"
                      >
                        {{ realtimeStats.connectionState }}
                      </q-chip>
                    </div>
                  </div>
                  
                  <div class="row">
                    <div class="col-6">Active Subscriptions:</div>
                    <div class="col-6">{{ realtimeStats.activeSubscriptions }}</div>
                  </div>
                  
                  <div class="row">
                    <div class="col-6">Total Events:</div>
                    <div class="col-6">{{ realtimeStats.totalEvents }}</div>
                  </div>
                  
                  <div class="row">
                    <div class="col-6">Average Latency:</div>
                    <div class="col-6">{{ realtimeStats.averageLatency.toFixed(1) }}ms</div>
                  </div>
                  
                  <div class="row">
                    <div class="col-6">Reconnect Count:</div>
                    <div class="col-6">{{ realtimeStats.reconnectCount }}</div>
                  </div>
                </div>

                <div class="q-mt-md">
                  <q-btn 
                    @click="reconnectRealtime" 
                    color="warning" 
                    size="sm" 
                    label="Reconnect"
                    icon="refresh"
                    :loading="reconnecting"
                  />
                </div>
              </q-card-section>
            </q-card>
          </div>
        </div>
      </q-card-section>

      <!-- Demo Actions -->
      <q-card-section>
        <div class="text-h6 q-mb-md">
          <q-icon name="play_arrow" class="q-mr-sm" />
          Demo Actions
        </div>
        
        <div class="row q-gutter-sm">
          <q-btn 
            @click="loadProfile" 
            color="primary" 
            label="Load Profile"
            icon="person"
            :loading="loadingProfile"
          />
          
          <q-btn 
            @click="loadProfileCached" 
            color="secondary" 
            label="Load Profile (Cached)"
            icon="cached"
            :loading="loadingProfile"
          />
          
          <q-btn 
            @click="invalidateProfile" 
            color="warning" 
            label="Invalidate Profile"
            icon="delete_sweep"
          />
          
          <q-btn 
            @click="testRealtime" 
            color="positive" 
            label="Test Real-time"
            icon="notifications"
            :loading="testingRealtime"
          />
        </div>
      </q-card-section>

      <!-- Results -->
      <q-card-section v-if="lastAction">
        <div class="text-h6 q-mb-md">
          <q-icon name="info" class="q-mr-sm" />
          Last Action Result
        </div>
        
        <q-card flat bordered class="bg-grey-1">
          <q-card-section>
            <div class="text-body2">
              <strong>Action:</strong> {{ lastAction.action }}
            </div>
            <div class="text-body2">
              <strong>Duration:</strong> {{ lastAction.duration }}ms
            </div>
            <div class="text-body2">
              <strong>Cache Hit:</strong> {{ lastAction.cacheHit ? 'Yes' : 'No' }}
            </div>
            <div class="text-body2" v-if="lastAction.result">
              <strong>Result:</strong> {{ lastAction.result }}
            </div>
          </q-card-section>
        </q-card>
      </q-card-section>
    </q-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useGlobalServicesStore } from '@/stores/globalServices'
import { useAuthStore } from '@/stores/auth'

// Services
const globalServices = useGlobalServicesStore()
const authStore = useAuthStore()
const cache = globalServices.cacheService
const realtime = globalServices.realtimeService
const profileManager = globalServices.profileManager

// Reactive state
const cacheStats = ref(cache.getStats())
const realtimeStats = ref(realtime.getStats())
const loadingProfile = ref(false)
const testingRealtime = ref(false)
const reconnecting = ref(false)
const lastAction = ref<any>(null)

// Computed
const connectionStateColor = computed(() => {
  switch (realtimeStats.value.connectionState) {
    case 'OPEN': return 'positive'
    case 'CONNECTING': return 'warning'
    case 'ERROR': return 'negative'
    default: return 'grey'
  }
})

// Methods
async function loadProfile() {
  const startTime = Date.now()
  loadingProfile.value = true
  
  try {
    const userId = authStore.currentUser?.id
    if (!userId) {
      throw new Error('No user logged in')
    }
    
    const profile = await profileManager.getProfile(userId, { forceRefresh: true })
    const duration = Date.now() - startTime
    
    lastAction.value = {
      action: 'Load Profile (Force Refresh)',
      duration,
      cacheHit: false,
      result: profile ? 'Profile loaded successfully' : 'No profile found'
    }
  } catch (error: any) {
    lastAction.value = {
      action: 'Load Profile (Force Refresh)',
      duration: Date.now() - startTime,
      cacheHit: false,
      result: `Error: ${error.message}`
    }
  } finally {
    loadingProfile.value = false
    refreshStats()
  }
}

async function loadProfileCached() {
  const startTime = Date.now()
  loadingProfile.value = true
  
  try {
    const userId = authStore.currentUser?.id
    if (!userId) {
      throw new Error('No user logged in')
    }
    
    const profile = await profileManager.getProfile(userId, { forceRefresh: false })
    const duration = Date.now() - startTime
    
    lastAction.value = {
      action: 'Load Profile (Allow Cache)',
      duration,
      cacheHit: duration < 50, // Assume cache hit if very fast
      result: profile ? 'Profile loaded successfully' : 'No profile found'
    }
  } catch (error: any) {
    lastAction.value = {
      action: 'Load Profile (Allow Cache)',
      duration: Date.now() - startTime,
      cacheHit: false,
      result: `Error: ${error.message}`
    }
  } finally {
    loadingProfile.value = false
    refreshStats()
  }
}

function invalidateProfile() {
  const startTime = Date.now()
  
  try {
    const userId = authStore.currentUser?.id
    if (!userId) {
      throw new Error('No user logged in')
    }
    
    profileManager.invalidateProfile(userId)
    const duration = Date.now() - startTime
    
    lastAction.value = {
      action: 'Invalidate Profile Cache',
      duration,
      cacheHit: false,
      result: 'Profile cache invalidated'
    }
  } catch (error: any) {
    lastAction.value = {
      action: 'Invalidate Profile Cache',
      duration: Date.now() - startTime,
      cacheHit: false,
      result: `Error: ${error.message}`
    }
  } finally {
    refreshStats()
  }
}

async function testRealtime() {
  testingRealtime.value = true
  
  try {
    // Subscribe to a test table
    const subscription = realtime.subscribe(
      {
        table: 'user_messages',
        event: '*'
      },
      (payload) => {
        console.log('Demo: Received real-time event', payload)
      },
      { deduplicate: true }
    )
    
    // Unsubscribe after 5 seconds
    setTimeout(() => {
      realtime.unsubscribe(subscription)
      lastAction.value = {
        action: 'Test Real-time Subscription',
        duration: 5000,
        cacheHit: false,
        result: 'Subscription created and cleaned up'
      }
      testingRealtime.value = false
      refreshStats()
    }, 5000)
    
  } catch (error: any) {
    lastAction.value = {
      action: 'Test Real-time Subscription',
      duration: 0,
      cacheHit: false,
      result: `Error: ${error.message}`
    }
    testingRealtime.value = false
  }
}

async function reconnectRealtime() {
  reconnecting.value = true
  
  try {
    await realtime.reconnect()
    lastAction.value = {
      action: 'Reconnect Real-time',
      duration: 1000,
      cacheHit: false,
      result: 'Real-time connection reestablished'
    }
  } catch (error: any) {
    lastAction.value = {
      action: 'Reconnect Real-time',
      duration: 1000,
      cacheHit: false,
      result: `Error: ${error.message}`
    }
  } finally {
    reconnecting.value = false
    refreshStats()
  }
}

function clearCache() {
  cache.clear()
  lastAction.value = {
    action: 'Clear All Cache',
    duration: 0,
    cacheHit: false,
    result: 'All cache entries cleared'
  }
  refreshStats()
}

function refreshStats() {
  cacheStats.value = cache.getStats()
  realtimeStats.value = realtime.getStats()
}

// Lifecycle
onMounted(() => {
  // Refresh stats every 2 seconds
  const interval = setInterval(refreshStats, 2000)
  
  onUnmounted(() => {
    clearInterval(interval)
  })
})
</script>

<style scoped>
.unified-services-demo {
  max-width: 1200px;
  margin: 0 auto;
}
</style>
