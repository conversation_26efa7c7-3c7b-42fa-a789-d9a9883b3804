/**
 * Main Application Integration Tests
 * 
 * Comprehensive tests to validate that the main app integration
 * works correctly with service coordination and error handling.
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { mount } from '@vue/test-utils';
import { createApp } from 'vue';
import { createP<PERSON>, setActivePinia } from 'pinia';
import { Quasar } from 'quasar';

// Import the components and plugins we're testing
import { createServiceCoordination } from '../../plugins/serviceCoordination';
import { createGlobalErrorHandler } from '../../utils/globalErrorHandler';
import { getFinalServiceCoordinationConfig } from '../../config/serviceCoordinationConfig';
import { useGlobalServicesStore } from '../../stores/globalServices';

describe('Main Application Integration', () => {
  let app: ReturnType<typeof createApp>;
  let pinia: ReturnType<typeof createPinia>;

  beforeEach(() => {
    // Create a fresh app instance for each test
    app = createApp({});
    pinia = createPinia();
    setActivePinia(pinia);
    
    // Mock console methods to avoid noise in tests
    vi.spyOn(console, 'log').mockImplementation(() => {});
    vi.spyOn(console, 'warn').mockImplementation(() => {});
    vi.spyOn(console, 'error').mockImplementation(() => {});
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('Plugin Installation Order', () => {
    it('should install plugins in the correct order', () => {
      const installOrder: string[] = [];
      
      // Mock plugin installations to track order
      const mockPinia = {
        install: vi.fn(() => installOrder.push('pinia'))
      };
      
      const mockErrorHandler = {
        install: vi.fn(() => installOrder.push('errorHandler'))
      };
      
      const mockServiceCoordination = {
        install: vi.fn(() => installOrder.push('serviceCoordination'))
      };

      app.use(mockPinia as any);
      app.use(mockErrorHandler as any);
      app.use(mockServiceCoordination as any);

      expect(installOrder).toEqual(['pinia', 'errorHandler', 'serviceCoordination']);
    });

    it('should handle plugin installation failures gracefully', () => {
      const failingPlugin = {
        install: vi.fn(() => {
          throw new Error('Plugin installation failed');
        })
      };

      expect(() => {
        app.use(failingPlugin as any);
      }).toThrow('Plugin installation failed');
    });
  });

  describe('Service Coordination Configuration', () => {
    it('should load correct configuration for development environment', () => {
      // Mock development environment
      const originalEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = 'development';

      const config = getFinalServiceCoordinationConfig();

      expect(config.enableDevTools).toBe(true);
      expect(config.enableAutoInitialization).toBe(true);
      expect(config.enableHealthMonitoring).toBe(true);
      expect(config.enableErrorRecovery).toBe(true);

      process.env.NODE_ENV = originalEnv;
    });

    it('should load correct configuration for production environment', () => {
      // Mock production environment
      const originalEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = 'production';

      const config = getFinalServiceCoordinationConfig();

      expect(config.enableDevTools).toBe(false);
      expect(config.enableAutoInitialization).toBe(true);
      expect(config.enableHealthMonitoring).toBe(true);
      expect(config.enableErrorRecovery).toBe(true);

      process.env.NODE_ENV = originalEnv;
    });

    it('should apply environment variable overrides', () => {
      // Mock environment variables
      const originalVars = {
        VITE_SERVICE_HEALTH_CHECK_INTERVAL: process.env.VITE_SERVICE_HEALTH_CHECK_INTERVAL,
        VITE_SERVICE_MAX_RETRY_ATTEMPTS: process.env.VITE_SERVICE_MAX_RETRY_ATTEMPTS
      };

      process.env.VITE_SERVICE_HEALTH_CHECK_INTERVAL = '5000';
      process.env.VITE_SERVICE_MAX_RETRY_ATTEMPTS = '5';

      const config = getFinalServiceCoordinationConfig();

      expect(config.healthCheckInterval).toBe(5000);
      expect(config.maxRetryAttempts).toBe(5);

      // Restore original environment variables
      Object.entries(originalVars).forEach(([key, value]) => {
        if (value === undefined) {
          delete process.env[key];
        } else {
          process.env[key] = value;
        }
      });
    });
  });

  describe('Service Coordination Plugin', () => {
    it('should create service coordination plugin with valid configuration', () => {
      const config = getFinalServiceCoordinationConfig();
      const plugin = createServiceCoordination(config);

      expect(plugin).toBeDefined();
      expect(plugin.install).toBeTypeOf('function');
      expect(plugin.coordinator).toBeDefined();
    });

    it('should initialize services when plugin is installed', async () => {
      app.use(pinia);
      
      const config = getFinalServiceCoordinationConfig();
      const plugin = createServiceCoordination(config);
      
      // Mock the coordinator initialization
      const mockInitialize = vi.fn().mockResolvedValue(true);
      plugin.coordinator.reinitializeServices = mockInitialize;

      app.use(plugin);

      // Wait for async initialization
      await new Promise(resolve => setTimeout(resolve, 100));

      // Verify initialization was attempted
      expect(mockInitialize).toHaveBeenCalled();
    });
  });

  describe('Global Error Handler', () => {
    it('should create global error handler', () => {
      const errorHandler = createGlobalErrorHandler();

      expect(errorHandler).toBeDefined();
      expect(errorHandler.install).toBeTypeOf('function');
      expect(errorHandler.errorHandler).toBeDefined();
    });

    it('should handle Vue component errors', () => {
      app.use(pinia);
      
      const errorHandler = createGlobalErrorHandler();
      const mockHandleError = vi.fn();
      errorHandler.errorHandler.handleError = mockHandleError;

      app.use(errorHandler);

      // Simulate a Vue component error
      const testError = new Error('Test component error');
      app.config.errorHandler?.(testError, null, 'component error');

      expect(mockHandleError).toHaveBeenCalledWith(
        testError,
        expect.objectContaining({
          component: 'Unknown',
          action: 'component error'
        })
      );
    });

    it('should provide error handler instance globally in development', () => {
      const originalEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = 'development';

      app.use(pinia);
      const errorHandler = createGlobalErrorHandler();
      app.use(errorHandler);

      // In a real browser environment, this would be available on window
      expect(errorHandler.errorHandler).toBeDefined();

      process.env.NODE_ENV = originalEnv;
    });
  });

  describe('Global Services Store Integration', () => {
    it('should initialize global services store', async () => {
      app.use(pinia);
      
      const globalServices = useGlobalServicesStore();
      
      expect(globalServices).toBeDefined();
      expect(globalServices.initializeAllServices).toBeTypeOf('function');
      expect(globalServices.serviceStatus).toBeDefined();
    });

    it('should provide access to all required services', async () => {
      app.use(pinia);
      
      const globalServices = useGlobalServicesStore();
      await globalServices.initializeAllServices();

      // Verify all services are accessible
      expect(globalServices.cacheService).toBeDefined();
      expect(globalServices.realtimeService).toBeDefined();
      expect(globalServices.activityService).toBeDefined();
      expect(globalServices.connectionService).toBeDefined();
      expect(globalServices.profileService).toBeDefined();
      expect(globalServices.profileManager).toBeDefined();
      expect(globalServices.profileCompletionService).toBeDefined();
      expect(globalServices.matchmakingService).toBeDefined();
      expect(globalServices.aiChatTriggerService).toBeDefined();
      expect(globalServices.aiEnhancedService).toBeDefined();
    });

    it('should handle service initialization failures gracefully', async () => {
      app.use(pinia);
      
      const globalServices = useGlobalServicesStore();
      
      // Mock a service initialization failure
      const originalInit = globalServices.initializeCacheService;
      globalServices.initializeCacheService = vi.fn().mockRejectedValue(new Error('Cache init failed'));

      try {
        await globalServices.initializeAllServices();
      } catch (error) {
        // Expected to fail
      }

      // Verify error is recorded
      expect(globalServices.serviceStatus.cache.error).toBeTruthy();
      
      // Restore original function
      globalServices.initializeCacheService = originalInit;
    });
  });

  describe('Development Tools Integration', () => {
    it('should expose development tools in development environment', () => {
      const originalEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = 'development';

      // Mock window object
      const mockWindow = {
        __SERVICE_DEV_TOOLS__: undefined,
        __SERVICE_DIAGNOSTICS__: undefined,
        __ERROR_HANDLER__: undefined
      };

      // Simulate development tools initialization
      mockWindow.__SERVICE_DEV_TOOLS__ = { getPerformanceMetrics: vi.fn() };
      mockWindow.__SERVICE_DIAGNOSTICS__ = vi.fn();
      mockWindow.__ERROR_HANDLER__ = { getErrorStatistics: vi.fn() };

      expect(mockWindow.__SERVICE_DEV_TOOLS__).toBeDefined();
      expect(mockWindow.__SERVICE_DIAGNOSTICS__).toBeTypeOf('function');
      expect(mockWindow.__ERROR_HANDLER__).toBeDefined();

      process.env.NODE_ENV = originalEnv;
    });

    it('should not expose development tools in production environment', () => {
      const originalEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = 'production';

      // In production, development tools should not be exposed
      // This is more of a documentation test since we can't easily test window globals
      expect(process.env.NODE_ENV).toBe('production');

      process.env.NODE_ENV = originalEnv;
    });
  });

  describe('Application Startup Sequence', () => {
    it('should complete full startup sequence without errors', async () => {
      const startupSteps: string[] = [];

      // Mock the complete startup sequence
      app.use(pinia);
      startupSteps.push('pinia-installed');

      const errorHandler = createGlobalErrorHandler();
      app.use(errorHandler);
      startupSteps.push('error-handler-installed');

      const config = getFinalServiceCoordinationConfig();
      const serviceCoordination = createServiceCoordination(config);
      app.use(serviceCoordination);
      startupSteps.push('service-coordination-installed');

      // Simulate service initialization
      const globalServices = useGlobalServicesStore();
      await globalServices.initializeAllServices();
      startupSteps.push('services-initialized');

      expect(startupSteps).toEqual([
        'pinia-installed',
        'error-handler-installed',
        'service-coordination-installed',
        'services-initialized'
      ]);
    });

    it('should handle startup failures gracefully', async () => {
      app.use(pinia);

      // Mock a startup failure
      const failingPlugin = {
        install: vi.fn(() => {
          throw new Error('Startup failure');
        })
      };

      expect(() => {
        app.use(failingPlugin as any);
      }).toThrow('Startup failure');

      // Application should still be in a recoverable state
      expect(app).toBeDefined();
    });
  });

  describe('Performance and Memory', () => {
    it('should not create memory leaks during initialization', async () => {
      const initialMemory = process.memoryUsage().heapUsed;

      // Perform multiple initialization cycles
      for (let i = 0; i < 10; i++) {
        const testApp = createApp({});
        const testPinia = createPinia();
        
        testApp.use(testPinia);
        testApp.use(createGlobalErrorHandler());
        testApp.use(createServiceCoordination(getFinalServiceCoordinationConfig()));
      }

      // Force garbage collection if available
      if (global.gc) {
        global.gc();
      }

      const finalMemory = process.memoryUsage().heapUsed;
      const memoryIncrease = finalMemory - initialMemory;

      // Memory increase should be reasonable (less than 10MB)
      expect(memoryIncrease).toBeLessThan(10 * 1024 * 1024);
    });

    it('should initialize services within reasonable time', async () => {
      app.use(pinia);
      
      const startTime = Date.now();
      
      const globalServices = useGlobalServicesStore();
      await globalServices.initializeAllServices();
      
      const endTime = Date.now();
      const initializationTime = endTime - startTime;

      // Initialization should complete within 5 seconds
      expect(initializationTime).toBeLessThan(5000);
    });
  });
});

/**
 * Practical validation function for browser testing
 */
export async function validateMainAppIntegration(): Promise<{
  success: boolean;
  results: Array<{ test: string; passed: boolean; error?: string }>;
  summary: string;
}> {
  const results: Array<{ test: string; passed: boolean; error?: string }> = [];

  // Test 1: Service coordination is available
  try {
    const coordinator = (window as any).__SERVICE_COORDINATOR__;
    results.push({
      test: 'Service Coordinator Available',
      passed: !!coordinator
    });
  } catch (error: any) {
    results.push({
      test: 'Service Coordinator Available',
      passed: false,
      error: error.message
    });
  }

  // Test 2: Global services are initialized
  try {
    const services = (window as any).__GLOBAL_SERVICES__;
    const allInitialized = services?.allInitialized;
    results.push({
      test: 'Global Services Initialized',
      passed: !!allInitialized
    });
  } catch (error: any) {
    results.push({
      test: 'Global Services Initialized',
      passed: false,
      error: error.message
    });
  }

  // Test 3: Error handler is active
  try {
    const errorHandler = (window as any).__ERROR_HANDLER__;
    results.push({
      test: 'Error Handler Active',
      passed: !!errorHandler
    });
  } catch (error: any) {
    results.push({
      test: 'Error Handler Active',
      passed: false,
      error: error.message
    });
  }

  // Test 4: Development tools are available (in development)
  try {
    const devTools = (window as any).__SERVICE_DEV_TOOLS__;
    const isDevEnv = process.env.NODE_ENV === 'development';
    results.push({
      test: 'Development Tools Available',
      passed: isDevEnv ? !!devTools : true // Pass if not dev env
    });
  } catch (error: any) {
    results.push({
      test: 'Development Tools Available',
      passed: false,
      error: error.message
    });
  }

  // Test 5: Service health monitoring
  try {
    const services = (window as any).__GLOBAL_SERVICES__;
    const health = services?.serviceHealth;
    results.push({
      test: 'Service Health Monitoring',
      passed: !!health && typeof health.status === 'string'
    });
  } catch (error: any) {
    results.push({
      test: 'Service Health Monitoring',
      passed: false,
      error: error.message
    });
  }

  const passedTests = results.filter(r => r.passed).length;
  const totalTests = results.length;
  const success = passedTests === totalTests;

  return {
    success,
    results,
    summary: `Main App Integration: ${passedTests}/${totalTests} tests passed`
  };
}

// Make available in browser console
if (typeof window !== 'undefined') {
  (window as any).__validateMainAppIntegration = validateMainAppIntegration;
}
