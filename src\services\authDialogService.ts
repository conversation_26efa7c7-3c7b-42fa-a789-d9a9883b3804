/**
 * Authentication Dialog Service
 * 
 * Global service for triggering authentication dialogs from anywhere in the application.
 * Uses event emitter pattern to communicate with the main layout component.
 */

import { ref } from 'vue';

export interface AuthDialogRequest {
  mode: 'signin' | 'signup';
  callback?: (success: boolean) => void;
}

// Global state for auth dialog
const authDialogState = ref<{
  isOpen: boolean;
  mode: 'signin' | 'signup';
  callback?: (success: boolean) => void;
}>({
  isOpen: false,
  mode: 'signin'
});

/**
 * Trigger authentication dialog
 */
export function triggerAuthDialog(mode: 'signin' | 'signup', callback?: (success: boolean) => void): Promise<boolean> {
  return new Promise((resolve) => {
    console.log(`Triggering ${mode} dialog`);
    
    // Set the dialog state
    authDialogState.value = {
      isOpen: true,
      mode,
      callback: (success: boolean) => {
        resolve(success);
        if (callback) callback(success);
      }
    };
  });
}

/**
 * Close authentication dialog
 */
export function closeAuthDialog(success: boolean = false): void {
  const currentState = authDialogState.value;
  
  // Call callback if provided
  if (currentState.callback) {
    currentState.callback(success);
  }
  
  // Reset state
  authDialogState.value = {
    isOpen: false,
    mode: 'signin'
  };
}

/**
 * Get current auth dialog state (for components to watch)
 */
export function useAuthDialogState() {
  return {
    authDialogState: authDialogState,
    triggerAuthDialog,
    closeAuthDialog
  };
}

/**
 * Check if auth dialog is currently open
 */
export function isAuthDialogOpen(): boolean {
  return authDialogState.value.isOpen;
}

/**
 * Get current auth dialog mode
 */
export function getAuthDialogMode(): 'signin' | 'signup' {
  return authDialogState.value.mode;
}
