/**
 * Global Services Store
 * 
 * This store centralizes access to all business logic services, ensuring:
 * 1. Single instance of each service throughout the application
 * 2. Proper initialization and dependency management
 * 3. Consistent state management
 * 4. Easier testing and mocking
 * 
 * All components should access services through this store instead of
 * importing services directly.
 */

import { defineStore } from 'pinia';
import { ref, computed, reactive } from 'vue';
import { useAuthStore } from './auth';
import { useProfileStore } from './profile';

// Import services
import { useUnifiedCache } from '@/services/unifiedCacheService';
import { useUnifiedRealtime } from '@/services/unifiedRealtimeService';
import { useActivityTrackingService } from '@/services/activityTrackingService';
import { useConnectionService } from '@/services/connectionService';
import { useProfileService } from '@/services/profileService';
import { ProfileManager } from '@/services/ProfileManager';
import { useProfileCompletion } from '@/services/profileCompletionService';
import { useMatchmakingService } from '@/services/matchmakingService';
import { AIChatTriggerService } from '@/services/aiChatTriggerService';
import * as aiEnhancedService from '@/services/aiEnhancedService';

// Service initialization status tracking
interface ServiceStatus {
  initialized: boolean;
  initializing: boolean;
  error: string | null;
  lastInitialized: Date | null;
}

export const useGlobalServicesStore = defineStore('globalServices', () => {
  // Dependencies
  const authStore = useAuthStore();
  const profileStore = useProfileStore();

  // Service instances (created once and reused)
  const cacheService = useUnifiedCache();
  const realtimeService = useUnifiedRealtime();
  const activityService = useActivityTrackingService();
  const connectionService = useConnectionService(activityService);
  const profileService = useProfileService();
  const profileManager = ProfileManager.getInstance();
  const profileCompletionService = useProfileCompletion();
  const matchmakingService = useMatchmakingService();
  const aiChatTriggerService = new AIChatTriggerService();

  // Service status tracking
  const serviceStatus = reactive<Record<string, ServiceStatus>>({
    cache: { initialized: false, initializing: false, error: null, lastInitialized: null },
    realtime: { initialized: false, initializing: false, error: null, lastInitialized: null },
    activity: { initialized: false, initializing: false, error: null, lastInitialized: null },
    connection: { initialized: false, initializing: false, error: null, lastInitialized: null },
    profile: { initialized: false, initializing: false, error: null, lastInitialized: null },
    profileManager: { initialized: false, initializing: false, error: null, lastInitialized: null },
    profileCompletion: { initialized: false, initializing: false, error: null, lastInitialized: null },
    matchmaking: { initialized: false, initializing: false, error: null, lastInitialized: null },
    aiChatTrigger: { initialized: false, initializing: false, error: null, lastInitialized: null }
  });

  // Global loading state
  const isInitializing = computed(() => 
    Object.values(serviceStatus).some(status => status.initializing)
  );

  const hasErrors = computed(() => 
    Object.values(serviceStatus).some(status => status.error !== null)
  );

  const allInitialized = computed(() =>
    Object.values(serviceStatus).every(status => status.initialized)
  );

  // Service health monitoring
  const serviceHealth = computed(() => {
    const services = Object.entries(serviceStatus);
    const healthy = services.filter(([_, status]) => status.initialized && !status.error).length;
    const total = services.length;
    const healthPercentage = (healthy / total) * 100;

    return {
      healthy,
      total,
      healthPercentage,
      status: healthPercentage === 100 ? 'HEALTHY' :
              healthPercentage >= 80 ? 'DEGRADED' : 'UNHEALTHY',
      failedServices: services
        .filter(([_, status]) => status.error)
        .map(([name, status]) => ({ name, error: status.error }))
    };
  });

  // Initialize all services with proper dependency management
  async function initializeAllServices() {
    console.log('GlobalServicesStore: Initializing all services with dependency coordination');

    try {
      // Phase 1: Initialize core infrastructure services (no dependencies)
      await Promise.all([
        initializeCacheService(),
        initializeRealtimeService()
      ]);

      // Phase 2: Initialize services that depend on core infrastructure
      await Promise.all([
        initializeActivityService(),
        initializeProfileServices()
      ]);

      // Phase 3: Initialize services that depend on previous phases
      await Promise.all([
        initializeConnectionService(), // Depends on activity service
        initializeMatchmakingService()
      ]);

      // Phase 4: Initialize AI services (may depend on all other services)
      await initializeAIChatTriggerService();

      console.log('GlobalServicesStore: All services initialized successfully');
      return allInitialized.value;
    } catch (error) {
      console.error('GlobalServicesStore: Failed to initialize services:', error);
      throw error;
    }
  }

  // Individual service initializers
  async function initializeCacheService() {
    if (serviceStatus.cache.initialized || serviceStatus.cache.initializing) return;
    
    try {
      serviceStatus.cache.initializing = true;
      // Cache service is self-initializing
      serviceStatus.cache.initialized = true;
      serviceStatus.cache.lastInitialized = new Date();
    } catch (error: any) {
      serviceStatus.cache.error = error.message;
      console.error('Failed to initialize cache service:', error);
    } finally {
      serviceStatus.cache.initializing = false;
    }
  }

  async function initializeRealtimeService() {
    if (serviceStatus.realtime.initialized || serviceStatus.realtime.initializing) return;
    
    try {
      serviceStatus.realtime.initializing = true;
      // Realtime service is self-initializing
      serviceStatus.realtime.initialized = true;
      serviceStatus.realtime.lastInitialized = new Date();
    } catch (error: any) {
      serviceStatus.realtime.error = error.message;
      console.error('Failed to initialize realtime service:', error);
    } finally {
      serviceStatus.realtime.initializing = false;
    }
  }

  async function initializeActivityService() {
    if (serviceStatus.activity.initialized || serviceStatus.activity.initializing) return;
    
    try {
      serviceStatus.activity.initializing = true;
      // Initialize activity service
      await activityService.initialize();
      serviceStatus.activity.initialized = true;
      serviceStatus.activity.lastInitialized = new Date();
    } catch (error: any) {
      serviceStatus.activity.error = error.message;
      console.error('Failed to initialize activity service:', error);
    } finally {
      serviceStatus.activity.initializing = false;
    }
  }

  async function initializeConnectionService() {
    if (serviceStatus.connection.initialized || serviceStatus.connection.initializing) return;
    
    try {
      serviceStatus.connection.initializing = true;
      // Connection service is self-initializing
      serviceStatus.connection.initialized = true;
      serviceStatus.connection.lastInitialized = new Date();
    } catch (error: any) {
      serviceStatus.connection.error = error.message;
      console.error('Failed to initialize connection service:', error);
    } finally {
      serviceStatus.connection.initializing = false;
    }
  }

  async function initializeProfileServices() {
    if (
      serviceStatus.profile.initialized || 
      serviceStatus.profile.initializing ||
      serviceStatus.profileManager.initializing ||
      serviceStatus.profileCompletion.initializing
    ) return;
    
    try {
      serviceStatus.profile.initializing = true;
      serviceStatus.profileManager.initializing = true;
      serviceStatus.profileCompletion.initializing = true;
      
      // Profile services are self-initializing
      serviceStatus.profile.initialized = true;
      serviceStatus.profileManager.initialized = true;
      serviceStatus.profileCompletion.initialized = true;
      
      serviceStatus.profile.lastInitialized = new Date();
      serviceStatus.profileManager.lastInitialized = new Date();
      serviceStatus.profileCompletion.lastInitialized = new Date();
    } catch (error: any) {
      serviceStatus.profile.error = error.message;
      serviceStatus.profileManager.error = error.message;
      serviceStatus.profileCompletion.error = error.message;
      console.error('Failed to initialize profile services:', error);
    } finally {
      serviceStatus.profile.initializing = false;
      serviceStatus.profileManager.initializing = false;
      serviceStatus.profileCompletion.initializing = false;
    }
  }

  async function initializeMatchmakingService() {
    if (serviceStatus.matchmaking.initialized || serviceStatus.matchmaking.initializing) return;
    
    try {
      serviceStatus.matchmaking.initializing = true;
      // Matchmaking service is self-initializing
      serviceStatus.matchmaking.initialized = true;
      serviceStatus.matchmaking.lastInitialized = new Date();
    } catch (error: any) {
      serviceStatus.matchmaking.error = error.message;
      console.error('Failed to initialize matchmaking service:', error);
    } finally {
      serviceStatus.matchmaking.initializing = false;
    }
  }

  async function initializeAIChatTriggerService() {
    if (serviceStatus.aiChatTrigger.initialized || serviceStatus.aiChatTrigger.initializing) return;
    
    try {
      serviceStatus.aiChatTrigger.initializing = true;
      // AI Chat Trigger service is self-initializing
      serviceStatus.aiChatTrigger.initialized = true;
      serviceStatus.aiChatTrigger.lastInitialized = new Date();
    } catch (error: any) {
      serviceStatus.aiChatTrigger.error = error.message;
      console.error('Failed to initialize AI chat trigger service:', error);
    } finally {
      serviceStatus.aiChatTrigger.initializing = false;
    }
  }

  // Service dependency validation
  function validateServiceDependencies(): { valid: boolean; issues: string[] } {
    const issues: string[] = [];

    // Check if core services are initialized before dependent services
    if (serviceStatus.connection.initialized && !serviceStatus.activity.initialized) {
      issues.push('Connection service initialized without activity service dependency');
    }

    if (serviceStatus.aiChatTrigger.initialized && !serviceStatus.cache.initialized) {
      issues.push('AI Chat Trigger service initialized without cache service dependency');
    }

    return {
      valid: issues.length === 0,
      issues
    };
  }

  // Service recovery mechanism
  async function recoverFailedServices(): Promise<void> {
    console.log('GlobalServicesStore: Attempting to recover failed services');

    const failedServices = Object.entries(serviceStatus)
      .filter(([_, status]) => status.error && !status.initializing)
      .map(([name]) => name);

    if (failedServices.length === 0) {
      console.log('GlobalServicesStore: No failed services to recover');
      return;
    }

    console.log('GlobalServicesStore: Recovering services:', failedServices);

    // Reset error states
    failedServices.forEach(serviceName => {
      serviceStatus[serviceName as keyof typeof serviceStatus].error = null;
    });

    // Reinitialize failed services
    await initializeAllServices();
  }

  // Graceful service shutdown
  async function shutdownAllServices(): Promise<void> {
    console.log('GlobalServicesStore: Shutting down all services');

    try {
      // Shutdown in reverse dependency order
      if (realtimeService && typeof realtimeService.disconnect === 'function') {
        await realtimeService.disconnect();
      }

      if (cacheService && typeof cacheService.clear === 'function') {
        cacheService.clear();
      }

      // Reset all service statuses
      Object.keys(serviceStatus).forEach(key => {
        serviceStatus[key as keyof typeof serviceStatus] = {
          initialized: false,
          initializing: false,
          error: null,
          lastInitialized: null
        };
      });

      console.log('GlobalServicesStore: All services shut down successfully');
    } catch (error) {
      console.error('GlobalServicesStore: Error during service shutdown:', error);
    }
  }

  return {
    // Service instances
    cacheService,
    realtimeService,
    activityService,
    connectionService,
    profileService,
    profileManager,
    profileCompletionService,
    matchmakingService,
    aiChatTriggerService,
    aiEnhancedService,

    // Status
    serviceStatus,
    isInitializing,
    hasErrors,
    allInitialized,
    serviceHealth,

    // Initializers
    initializeAllServices,
    initializeCacheService,
    initializeRealtimeService,
    initializeActivityService,
    initializeConnectionService,
    initializeProfileServices,
    initializeMatchmakingService,
    initializeAIChatTriggerService,

    // Coordination and Recovery
    validateServiceDependencies,
    recoverFailedServices,
    shutdownAllServices
  };
});
