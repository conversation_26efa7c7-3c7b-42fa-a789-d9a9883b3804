/**
 * Development Tools for Service Debugging and Monitoring
 * 
 * Provides comprehensive debugging tools for the service coordination system
 * and global services store. Only available in development environment.
 */

import { useGlobalServicesStore } from '../stores/globalServices';
import { validateStateManagementMigration } from './validateStateManagementMigration';

export interface ServiceDebugInfo {
  name: string;
  status: 'initialized' | 'initializing' | 'error' | 'not_initialized';
  error?: string;
  lastInitialized?: Date;
  methods: string[];
  dependencies: string[];
}

export interface PerformanceMetrics {
  serviceInitializationTime: number;
  cacheHitRate: number;
  memoryUsage: number;
  activeSubscriptions: number;
  errorCount: number;
}

export class ServiceDevelopmentTools {
  private globalServices: ReturnType<typeof useGlobalServicesStore>;
  private performanceMetrics: PerformanceMetrics;
  private startTime: number;

  constructor() {
    this.globalServices = useGlobalServicesStore();
    this.startTime = Date.now();
    this.performanceMetrics = {
      serviceInitializationTime: 0,
      cacheHitRate: 0,
      memoryUsage: 0,
      activeSubscriptions: 0,
      errorCount: 0
    };
  }

  /**
   * Get comprehensive service debug information
   */
  getServiceDebugInfo(): ServiceDebugInfo[] {
    const services = [
      {
        name: 'Cache Service',
        service: this.globalServices.cacheService,
        dependencies: [],
        methods: ['get', 'set', 'invalidate', 'clear', 'getStats']
      },
      {
        name: 'Realtime Service',
        service: this.globalServices.realtimeService,
        dependencies: [],
        methods: ['subscribe', 'unsubscribe', 'disconnect', 'getStats']
      },
      {
        name: 'Activity Service',
        service: this.globalServices.activityService,
        dependencies: ['Cache Service'],
        methods: ['trackActivity', 'getRecentActivity', 'getUserActivity']
      },
      {
        name: 'Connection Service',
        service: this.globalServices.connectionService,
        dependencies: ['Activity Service'],
        methods: ['connectWithUser', 'getConnections', 'removeConnection']
      },
      {
        name: 'Profile Service',
        service: this.globalServices.profileService,
        dependencies: ['Cache Service'],
        methods: ['formatProfileType', 'getProfile', 'updateProfile']
      },
      {
        name: 'Profile Manager',
        service: this.globalServices.profileManager,
        dependencies: ['Cache Service', 'Profile Service'],
        methods: ['invalidateProfile', 'getProfileData', 'updateProfileData']
      },
      {
        name: 'AI Chat Trigger Service',
        service: this.globalServices.aiChatTriggerService,
        dependencies: ['Cache Service'],
        methods: ['triggerChat', 'getTriggersForProfile']
      },
      {
        name: 'AI Enhanced Service',
        service: this.globalServices.aiEnhancedService,
        dependencies: ['Cache Service'],
        methods: ['sendEnhancedChatMessage', 'getConversationHistory']
      }
    ];

    return services.map(({ name, service, dependencies, methods }) => {
      const status = this.globalServices.serviceStatus[this.getServiceKey(name)];
      
      return {
        name,
        status: status?.error ? 'error' : 
                status?.initializing ? 'initializing' :
                status?.initialized ? 'initialized' : 'not_initialized',
        error: status?.error || undefined,
        lastInitialized: status?.lastInitialized ? new Date(status.lastInitialized) : undefined,
        methods: methods.filter(method => service && typeof (service as any)[method] === 'function'),
        dependencies
      };
    });
  }

  /**
   * Get service key from service name
   */
  private getServiceKey(serviceName: string): string {
    const keyMap: Record<string, string> = {
      'Cache Service': 'cache',
      'Realtime Service': 'realtime',
      'Activity Service': 'activity',
      'Connection Service': 'connection',
      'Profile Service': 'profile',
      'Profile Manager': 'profileManager',
      'AI Chat Trigger Service': 'aiChatTrigger',
      'AI Enhanced Service': 'aiEnhanced'
    };
    return keyMap[serviceName] || serviceName.toLowerCase().replace(/\s+/g, '');
  }

  /**
   * Get current performance metrics
   */
  async getPerformanceMetrics(): Promise<PerformanceMetrics> {
    try {
      // Service initialization time
      this.performanceMetrics.serviceInitializationTime = Date.now() - this.startTime;

      // Cache hit rate
      if (this.globalServices.cacheService && typeof this.globalServices.cacheService.getStats === 'function') {
        const cacheStats = this.globalServices.cacheService.getStats();
        this.performanceMetrics.cacheHitRate = cacheStats.hitRate || 0;
      }

      // Memory usage (approximate)
      if (performance && (performance as any).memory) {
        this.performanceMetrics.memoryUsage = (performance as any).memory.usedJSHeapSize;
      }

      // Active subscriptions
      if (this.globalServices.realtimeService && typeof this.globalServices.realtimeService.getStats === 'function') {
        const realtimeStats = this.globalServices.realtimeService.getStats();
        this.performanceMetrics.activeSubscriptions = realtimeStats.activeSubscriptions || 0;
      }

      // Error count
      const serviceStatuses = Object.values(this.globalServices.serviceStatus);
      this.performanceMetrics.errorCount = serviceStatuses.filter(status => status.error).length;

      return { ...this.performanceMetrics };
    } catch (error) {
      console.error('Error getting performance metrics:', error);
      return this.performanceMetrics;
    }
  }

  /**
   * Run comprehensive service diagnostics
   */
  async runDiagnostics(): Promise<{
    serviceInfo: ServiceDebugInfo[];
    performanceMetrics: PerformanceMetrics;
    healthStatus: any;
    validationReport: any;
  }> {
    console.log('🔍 Running service diagnostics...');

    const serviceInfo = this.getServiceDebugInfo();
    const performanceMetrics = await this.getPerformanceMetrics();
    const healthStatus = this.globalServices.serviceHealth;
    const validationReport = await validateStateManagementMigration();

    const report = {
      serviceInfo,
      performanceMetrics,
      healthStatus,
      validationReport
    };

    console.log('📊 Diagnostics Report:', report);
    return report;
  }

  /**
   * Test service functionality
   */
  async testServices(): Promise<{ [serviceName: string]: boolean }> {
    console.log('🧪 Testing service functionality...');
    
    const results: { [serviceName: string]: boolean } = {};

    // Test cache service
    try {
      const cache = this.globalServices.cacheService;
      cache.set('test-key', 'test-value');
      const value = cache.get('test-key');
      results['Cache Service'] = value === 'test-value';
      cache.invalidate('test-key');
    } catch (error) {
      results['Cache Service'] = false;
    }

    // Test AI services
    try {
      const aiService = this.globalServices.aiChatTriggerService;
      const triggers = aiService.getTriggersForProfile('mentor');
      results['AI Chat Trigger Service'] = typeof triggers === 'object';
    } catch (error) {
      results['AI Chat Trigger Service'] = false;
    }

    // Test profile service
    try {
      const profileService = this.globalServices.profileService;
      const formatted = profileService.formatProfileType('mentor');
      results['Profile Service'] = typeof formatted === 'string';
    } catch (error) {
      results['Profile Service'] = false;
    }

    console.log('🧪 Service Test Results:', results);
    return results;
  }

  /**
   * Monitor service health continuously
   */
  startHealthMonitoring(interval: number = 10000): () => void {
    console.log('📊 Starting continuous health monitoring...');
    
    const monitoringInterval = setInterval(async () => {
      const health = this.globalServices.serviceHealth;
      const metrics = await this.getPerformanceMetrics();
      
      console.log('💓 Health Check:', {
        status: health.status,
        healthPercentage: health.healthPercentage,
        failedServices: health.failedServices.length,
        cacheHitRate: metrics.cacheHitRate,
        memoryUsage: `${(metrics.memoryUsage / 1024 / 1024).toFixed(2)}MB`,
        activeSubscriptions: metrics.activeSubscriptions
      });

      // Alert on issues
      if (health.status !== 'HEALTHY') {
        console.warn('⚠️ Service health issue detected:', health.failedServices);
      }

      if (metrics.cacheHitRate < 0.8) {
        console.warn('⚠️ Low cache hit rate:', metrics.cacheHitRate);
      }
    }, interval);

    return () => {
      clearInterval(monitoringInterval);
      console.log('📊 Health monitoring stopped');
    };
  }

  /**
   * Force service recovery
   */
  async forceServiceRecovery(): Promise<void> {
    console.log('🔧 Forcing service recovery...');
    
    try {
      await this.globalServices.recoverFailedServices();
      console.log('✅ Service recovery completed');
    } catch (error) {
      console.error('❌ Service recovery failed:', error);
    }
  }

  /**
   * Reset all services
   */
  async resetAllServices(): Promise<void> {
    console.log('🔄 Resetting all services...');
    
    try {
      await this.globalServices.shutdownAllServices();
      await this.globalServices.initializeAllServices();
      console.log('✅ All services reset successfully');
    } catch (error) {
      console.error('❌ Service reset failed:', error);
    }
  }
}

/**
 * Initialize development tools and expose them globally
 */
export function initializeDevelopmentTools(): void {
  if (import.meta.env.MODE !== 'development') {
    return;
  }

  const devTools = new ServiceDevelopmentTools();

  // Expose tools to global scope for console access
  if (typeof window !== 'undefined') {
    (window as any).__SERVICE_DEV_TOOLS__ = devTools;
    (window as any).__SERVICE_DIAGNOSTICS__ = () => devTools.runDiagnostics();
    (window as any).__SERVICE_TEST__ = () => devTools.testServices();
    (window as any).__SERVICE_RECOVERY__ = () => devTools.forceServiceRecovery();
    (window as any).__SERVICE_RESET__ = () => devTools.resetAllServices();

    console.log('🔧 Development tools loaded. Available commands:');
    console.log('  __SERVICE_DEV_TOOLS__ - Access to development tools instance');
    console.log('  __SERVICE_DIAGNOSTICS__() - Run comprehensive diagnostics');
    console.log('  __SERVICE_TEST__() - Test all service functionality');
    console.log('  __SERVICE_RECOVERY__() - Force service recovery');
    console.log('  __SERVICE_RESET__() - Reset all services');
  }
}

// Auto-initialize in development
if (import.meta.env.MODE === 'development') {
  // Delay initialization to ensure services are loaded
  setTimeout(initializeDevelopmentTools, 1000);
}
