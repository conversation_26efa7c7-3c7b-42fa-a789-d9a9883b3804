import { test, expect } from '@playwright/test';

test.describe('News Ticker and AI Chat Integration', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the home page
    await page.goto('/');
    
    // Wait for the page to load completely
    await page.waitForLoadState('networkidle');
  });

  test('News ticker should be visible on home page', async ({ page }) => {
    // Check if news ticker container exists
    const newsTicker = page.locator('.news-ticker-container');
    await expect(newsTicker).toBeVisible({ timeout: 10000 });

    // Check if ticker has content or fallback content
    const newsItems = page.locator('.news-item');
    const tickerMinimized = page.locator('.ticker-minimized');
    
    // Either news items should be visible or ticker should be minimized
    const hasContent = await newsItems.count() > 0;
    const isMinimized = await tickerMinimized.isVisible();
    
    expect(hasContent || isMinimized).toBeTruthy();
  });

  test('News ticker toggle functionality', async ({ page }) => {
    // Wait for news ticker to be visible
    const newsTicker = page.locator('.news-ticker-container');
    await expect(newsTicker).toBeVisible({ timeout: 10000 });

    // Find and click the toggle button
    const toggleButton = page.locator('.ticker-toggle-btn');
    await expect(toggleButton).toBeVisible();
    
    // Check initial state
    const initiallyMinimized = await page.locator('.ticker-minimized').isVisible();
    
    // Click toggle button
    await toggleButton.click();
    
    // Wait for animation to complete
    await page.waitForTimeout(500);
    
    // Check that state has changed
    const afterToggleMinimized = await page.locator('.ticker-minimized').isVisible();
    expect(afterToggleMinimized).toBe(!initiallyMinimized);
  });

  test('AI Chat Assistant should be visible on home page', async ({ page }) => {
    // Check if AI chat toggle button exists
    const aiChatToggle = page.locator('[data-testid="ai-chat-toggle"]');
    await expect(aiChatToggle).toBeVisible({ timeout: 10000 });

    // Check if button has correct icon and tooltip
    await expect(aiChatToggle).toHaveAttribute('icon', 'psychology');
  });

  test('AI Chat Assistant can be opened and closed', async ({ page }) => {
    // Find and click the AI chat toggle button
    const aiChatToggle = page.locator('[data-testid="ai-chat-toggle"]');
    await expect(aiChatToggle).toBeVisible({ timeout: 10000 });
    
    await aiChatToggle.click();
    
    // Wait for chat window to appear
    const chatWindow = page.locator('.chat-window');
    await expect(chatWindow).toBeVisible({ timeout: 5000 });
    
    // Check if chat header is visible
    const chatHeader = page.locator('.chat-header');
    await expect(chatHeader).toBeVisible();
    await expect(chatHeader).toContainText('ZbInnovation AI');
    
    // Check if input field is visible
    const chatInput = page.locator('[data-testid="ai-chat-input"]');
    await expect(chatInput).toBeVisible();
    
    // Close the chat
    const closeButton = page.locator('.chat-header q-btn[icon="close"]');
    await closeButton.click();
    
    // Verify chat window is closed
    await expect(chatWindow).not.toBeVisible();
    await expect(aiChatToggle).toBeVisible();
  });

  test('AI Chat positioning adjusts for news ticker', async ({ page }) => {
    // Check if both components are visible
    const newsTicker = page.locator('.news-ticker-container');
    const aiChatToggle = page.locator('[data-testid="ai-chat-toggle"]');
    
    await expect(newsTicker).toBeVisible({ timeout: 10000 });
    await expect(aiChatToggle).toBeVisible({ timeout: 10000 });
    
    // Check if AI chat has landing page positioning class
    await expect(aiChatToggle).toHaveClass(/landing-position/);
  });

  test('News ticker handles errors gracefully', async ({ page }) => {
    // Mock network failure for news items
    await page.route('**/rest/v1/news_items*', route => {
      route.abort('failed');
    });
    
    // Reload page to trigger the error
    await page.reload();
    await page.waitForLoadState('networkidle');
    
    // Check if fallback content is shown
    const newsTicker = page.locator('.news-ticker-container');
    await expect(newsTicker).toBeVisible({ timeout: 10000 });
    
    // Should show fallback content
    const fallbackContent = page.locator('.news-item').filter({ hasText: 'News Service Temporarily Unavailable' });
    await expect(fallbackContent).toBeVisible({ timeout: 5000 });
  });

  test('AI Chat handles initialization errors gracefully', async ({ page }) => {
    // Mock AI service failure
    await page.route('**/functions/v1/ai-enhanced-chat*', route => {
      route.abort('failed');
    });
    
    // Open AI chat
    const aiChatToggle = page.locator('[data-testid="ai-chat-toggle"]');
    await expect(aiChatToggle).toBeVisible({ timeout: 10000 });
    await aiChatToggle.click();
    
    // Chat should still open even with service errors
    const chatWindow = page.locator('.chat-window');
    await expect(chatWindow).toBeVisible({ timeout: 5000 });
    
    // Input should be available
    const chatInput = page.locator('[data-testid="ai-chat-input"]');
    await expect(chatInput).toBeVisible();
  });

  test('Components work together without conflicts', async ({ page }) => {
    // Both components should be visible
    const newsTicker = page.locator('.news-ticker-container');
    const aiChatToggle = page.locator('[data-testid="ai-chat-toggle"]');
    
    await expect(newsTicker).toBeVisible({ timeout: 10000 });
    await expect(aiChatToggle).toBeVisible({ timeout: 10000 });
    
    // Open AI chat
    await aiChatToggle.click();
    const chatWindow = page.locator('.chat-window');
    await expect(chatWindow).toBeVisible({ timeout: 5000 });
    
    // News ticker should still be functional
    const toggleButton = page.locator('.ticker-toggle-btn');
    await expect(toggleButton).toBeVisible();
    await toggleButton.click();
    
    // Both should work independently
    await expect(chatWindow).toBeVisible();
    
    // Close chat
    const closeButton = page.locator('.chat-header q-btn[icon="close"]');
    await closeButton.click();
    await expect(chatWindow).not.toBeVisible();
    
    // News ticker should still be visible
    await expect(newsTicker).toBeVisible();
  });

  test('News ticker shows appropriate content', async ({ page }) => {
    const newsTicker = page.locator('.news-ticker-container');
    await expect(newsTicker).toBeVisible({ timeout: 10000 });
    
    // Check if news items have required structure
    const newsItems = page.locator('.news-item');
    const count = await newsItems.count();
    
    if (count > 0) {
      // Check first news item structure
      const firstItem = newsItems.first();
      await expect(firstItem.locator('.news-category')).toBeVisible();
      await expect(firstItem.locator('.news-title')).toBeVisible();
      await expect(firstItem.locator('.news-separator')).toBeVisible();
    } else {
      // Should show minimized view or fallback content
      const minimizedView = page.locator('.ticker-minimized');
      await expect(minimizedView).toBeVisible();
    }
  });
});
