import { test, expect } from '@playwright/test';

// Test configuration
const BASE_URL = 'http://localhost:5173';
const TEST_USER_EMAIL = '<EMAIL>';
const TEST_USER_PASSWORD = 'Superb@23';

test.describe('AI Functionality Comprehensive Tests', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the home page
    await page.goto(BASE_URL);
    
    // Wait for the page to load
    await page.waitForLoadState('networkidle');
  });

  test.describe('AI Chat Interface', () => {
    test('should display AI chat button on landing page', async ({ page }) => {
      // Check if AI chat button is visible
      const aiChatButton = page.locator('[data-testid="ai-chat-toggle"]');
      await expect(aiChatButton).toBeVisible();
    });

    test('should open AI chat window when button is clicked', async ({ page }) => {
      // Click AI chat button
      await page.locator('[data-testid="ai-chat-toggle"]').click();
      
      // Check if chat window is visible
      const chatWindow = page.locator('[data-testid="ai-chat-container"]');
      await expect(chatWindow).toBeVisible();
      
      // Check if welcome message is displayed
      const welcomeMessage = page.locator('text=Welcome to ZbInnovation!');
      await expect(welcomeMessage).toBeVisible();
      
      // Check if suggestions are displayed
      const suggestions = page.locator('[data-testid="ai-chat-suggestions"]');
      await expect(suggestions).toBeVisible();
    });

    test('should close AI chat when close button is clicked', async ({ page }) => {
      // Open AI chat
      await page.locator('[data-testid="ai-chat-toggle"]').click();
      
      // Wait for chat to open
      await expect(page.locator('[data-testid="ai-chat-container"]')).toBeVisible();
      
      // Click close button
      await page.locator('[data-testid="ai-chat-close"]').click();
      
      // Check if chat window is hidden
      await expect(page.locator('[data-testid="ai-chat-container"]')).not.toBeVisible();
    });
  });

  test.describe('AI Message Sending', () => {
    test('should send message and receive AI response', async ({ page }) => {
      // Open AI chat
      await page.locator('[data-testid="ai-chat-toggle"]').click();
      
      // Type a message
      const testMessage = 'Hello, how can you help me?';
      await page.locator('[data-testid="ai-chat-input"]').fill(testMessage);
      
      // Send the message
      await page.locator('[data-testid="ai-chat-send"]').click();
      
      // Check if user message appears
      const userMessage = page.locator(`text=${testMessage}`);
      await expect(userMessage).toBeVisible();
      
      // Wait for AI response (with timeout)
      await page.waitForSelector('[data-testid="ai-message"]', { timeout: 30000 });
      
      // Check if AI response appears
      const aiResponse = page.locator('[data-testid="ai-message"]').last();
      await expect(aiResponse).toBeVisible();
    });

    test('should handle quick suggestion clicks', async ({ page }) => {
      // Open AI chat
      await page.locator('[data-testid="ai-chat-toggle"]').click();
      
      // Click on a quick suggestion
      await page.locator('text=How do I sign up for the platform?').click();
      
      // Check if the suggestion appears as a user message
      const userMessage = page.locator('text=How do I sign up for the platform?');
      await expect(userMessage).toBeVisible();
      
      // Wait for AI response
      await page.waitForSelector('[data-testid="ai-message"]', { timeout: 30000 });
    });

    test('should disable send button when input is empty', async ({ page }) => {
      // Open AI chat
      await page.locator('[data-testid="ai-chat-toggle"]').click();
      
      // Check if send button is disabled when input is empty
      const sendButton = page.locator('[data-testid="ai-chat-send"]');
      await expect(sendButton).toBeDisabled();
      
      // Type a message
      await page.locator('[data-testid="ai-chat-input"]').fill('Test message');
      
      // Check if send button is enabled
      await expect(sendButton).toBeEnabled();
      
      // Clear the input
      await page.locator('[data-testid="ai-chat-input"]').fill('');
      
      // Check if send button is disabled again
      await expect(sendButton).toBeDisabled();
    });
  });

  test.describe('AI Streaming Functionality', () => {
    test('should display loading indicator during AI response', async ({ page }) => {
      // Open AI chat
      await page.locator('[data-testid="ai-chat-toggle"]').click();
      
      // Send a message
      await page.locator('[data-testid="ai-chat-input"]').fill('Tell me about innovation');
      await page.locator('[data-testid="ai-chat-send"]').click();
      
      // Check if loading indicator appears
      const loadingIndicator = page.locator('[data-testid="ai-loading"]');
      await expect(loadingIndicator).toBeVisible();
      
      // Wait for response to complete
      await page.waitForSelector('[data-testid="ai-message"]', { timeout: 30000 });
      
      // Check if loading indicator disappears
      await expect(loadingIndicator).not.toBeVisible();
    });

    test('should handle streaming response correctly', async ({ page }) => {
      // Listen for console logs to verify streaming
      const consoleLogs: string[] = [];
      page.on('console', msg => {
        if (msg.type() === 'log' && msg.text().includes('chunk')) {
          consoleLogs.push(msg.text());
        }
      });
      
      // Open AI chat
      await page.locator('[data-testid="ai-chat-toggle"]').click();
      
      // Send a message
      await page.locator('[data-testid="ai-chat-input"]').fill('Explain the innovation ecosystem');
      await page.locator('[data-testid="ai-chat-send"]').click();
      
      // Wait for response
      await page.waitForSelector('[data-testid="ai-message"]', { timeout: 30000 });
      
      // Check if streaming chunks were received (optional - depends on implementation)
      // This test verifies that the streaming mechanism is working
    });
  });

  test.describe('AI Action Buttons', () => {
    test('should display action buttons for unauthenticated users', async ({ page }) => {
      // Open AI chat
      await page.locator('[data-testid="ai-chat-toggle"]').click();
      
      // Send a message to trigger AI response with action buttons
      await page.locator('[data-testid="ai-chat-input"]').fill('How do I get started?');
      await page.locator('[data-testid="ai-chat-send"]').click();
      
      // Wait for response with action buttons
      await page.waitForSelector('[data-testid="ai-action-button"]', { timeout: 30000 });
      
      // Check for unauthenticated user action buttons
      const actionButtons = page.locator('[data-testid="ai-action-button"]');
      const buttonCount = await actionButtons.count();
      expect(buttonCount).toBeGreaterThan(0);
      
      // Check for specific buttons
      const signUpButton = page.locator('text=Sign Up');
      const exploreButton = page.locator('text=Explore Community');
      
      // At least one of these should be visible
      const signUpVisible = await signUpButton.isVisible();
      const exploreVisible = await exploreButton.isVisible();
      expect(signUpVisible || exploreVisible).toBe(true);
    });

    test('should navigate correctly when action buttons are clicked', async ({ page }) => {
      // Open AI chat
      await page.locator('[data-testid="ai-chat-toggle"]').click();
      
      // Send a message to get action buttons
      await page.locator('[data-testid="ai-chat-input"]').fill('Show me the community');
      await page.locator('[data-testid="ai-chat-send"]').click();
      
      // Wait for action buttons
      await page.waitForSelector('[data-testid="ai-action-button"]', { timeout: 30000 });
      
      // Find and click an action button (if available)
      const exploreButton = page.locator('text=Explore Community').first();
      if (await exploreButton.isVisible()) {
        await exploreButton.click();
        
        // Check if navigation occurred
        await page.waitForURL('**/virtual-community**', { timeout: 10000 });
        expect(page.url()).toContain('virtual-community');
      }
    });
  });

  test.describe('AI Error Handling', () => {
    test('should handle network errors gracefully', async ({ page }) => {
      // Intercept and block the AI chat API call
      await page.route('**/functions/v1/ai-enhanced-chat', route => {
        route.abort('failed');
      });
      
      // Open AI chat
      await page.locator('[data-testid="ai-chat-toggle"]').click();
      
      // Send a message
      await page.locator('[data-testid="ai-chat-input"]').fill('This should fail');
      await page.locator('[data-testid="ai-chat-send"]').click();
      
      // Wait a moment for the error to be handled
      await page.waitForTimeout(5000);
      
      // Check if loading state is cleared
      const loadingIndicator = page.locator('[data-testid="ai-loading"]');
      await expect(loadingIndicator).not.toBeVisible();
      
      // Check if error message is displayed (if implemented)
      // This depends on your error handling implementation
    });
  });

  test.describe('AI Performance', () => {
    test('should respond within reasonable time limits', async ({ page }) => {
      // Open AI chat
      await page.locator('[data-testid="ai-chat-toggle"]').click();
      
      // Measure response time
      const startTime = Date.now();
      
      // Send a message
      await page.locator('[data-testid="ai-chat-input"]').fill('Quick test message');
      await page.locator('[data-testid="ai-chat-send"]').click();
      
      // Wait for response
      await page.waitForSelector('[data-testid="ai-message"]', { timeout: 30000 });
      
      const endTime = Date.now();
      const responseTime = endTime - startTime;
      
      // Response should be within 30 seconds
      expect(responseTime).toBeLessThan(30000);
      
      console.log(`AI response time: ${responseTime}ms`);
    });
  });

  test.describe('AI Context Awareness', () => {
    test('should provide different suggestions for unauthenticated users', async ({ page }) => {
      // Open AI chat
      await page.locator('[data-testid="ai-chat-toggle"]').click();
      
      // Check for unauthenticated user suggestions
      const suggestions = [
        'How do I sign up for the platform?',
        'What features are available?',
        'Tell me about the innovation community',
        'How can I connect with investors?'
      ];
      
      for (const suggestion of suggestions) {
        const suggestionElement = page.locator(`text=${suggestion}`);
        await expect(suggestionElement).toBeVisible();
      }
    });
  });
});
