/**
 * State Management Migration Validation Script
 * 
 * Practical validation script to test that the migration from direct service
 * imports to state management is working correctly.
 */

import { useGlobalServicesStore } from '../stores/globalServices';
import { useServiceInitialization } from '../composables/useServiceInitialization';

export interface ValidationResult {
  testName: string;
  passed: boolean;
  details: string;
  error?: string;
  duration: number;
}

export interface MigrationValidationReport {
  overallStatus: 'PASSED' | 'FAILED' | 'PARTIAL';
  totalTests: number;
  passedTests: number;
  failedTests: number;
  results: ValidationResult[];
  summary: string;
}

export class StateManagementMigrationValidator {
  private results: ValidationResult[] = [];

  async validateMigration(): Promise<MigrationValidationReport> {
    console.log('🔍 Starting State Management Migration Validation...');
    
    this.results = [];

    // Run all validation tests
    await this.testGlobalServicesStoreInitialization();
    await this.testServiceAccessibility();
    await this.testServiceDependencies();
    await this.testServiceCoordination();
    await this.testErrorHandling();
    await this.testPerformance();
    await this.testMemoryManagement();

    return this.generateReport();
  }

  private async runTest(testName: string, testFn: () => Promise<void>): Promise<void> {
    const startTime = Date.now();
    
    try {
      await testFn();
      
      this.results.push({
        testName,
        passed: true,
        details: 'Test completed successfully',
        duration: Date.now() - startTime
      });
      
      console.log(`✅ ${testName} - PASSED`);
    } catch (error: any) {
      this.results.push({
        testName,
        passed: false,
        details: 'Test failed',
        error: error.message,
        duration: Date.now() - startTime
      });
      
      console.log(`❌ ${testName} - FAILED: ${error.message}`);
    }
  }

  private async testGlobalServicesStoreInitialization(): Promise<void> {
    await this.runTest('Global Services Store Initialization', async () => {
      const globalServices = useGlobalServicesStore();
      
      if (!globalServices) {
        throw new Error('Global services store is not available');
      }

      const initialized = await globalServices.initializeAllServices();
      
      if (!initialized) {
        throw new Error('Failed to initialize all services');
      }

      if (!globalServices.allInitialized) {
        throw new Error('Not all services are marked as initialized');
      }
    });
  }

  private async testServiceAccessibility(): Promise<void> {
    await this.runTest('Service Accessibility', async () => {
      const globalServices = useGlobalServicesStore();
      await globalServices.initializeAllServices();

      const requiredServices = [
        'cacheService',
        'realtimeService',
        'activityService',
        'connectionService',
        'profileService',
        'profileManager',
        'profileCompletionService',
        'matchmakingService',
        'aiChatTriggerService',
        'aiEnhancedService'
      ];

      for (const serviceName of requiredServices) {
        const service = (globalServices as any)[serviceName];
        if (!service) {
          throw new Error(`Service ${serviceName} is not accessible`);
        }
      }
    });
  }

  private async testServiceDependencies(): Promise<void> {
    await this.runTest('Service Dependencies', async () => {
      const globalServices = useGlobalServicesStore();
      await globalServices.initializeAllServices();

      const validation = globalServices.validateServiceDependencies();
      
      if (!validation.valid) {
        throw new Error(`Service dependency validation failed: ${validation.issues.join(', ')}`);
      }
    });
  }

  private async testServiceCoordination(): Promise<void> {
    await this.runTest('Service Coordination', async () => {
      const { initializeServices, isReady, hasErrors } = useServiceInitialization({
        autoInitialize: false,
        maxRetries: 2
      });

      const result = await initializeServices();
      
      if (!result) {
        throw new Error('Service initialization through composable failed');
      }

      if (!isReady.value) {
        throw new Error('Services are not ready after initialization');
      }

      if (hasErrors.value) {
        throw new Error('Services have errors after initialization');
      }
    });
  }

  private async testErrorHandling(): Promise<void> {
    await this.runTest('Error Handling and Recovery', async () => {
      const globalServices = useGlobalServicesStore();
      await globalServices.initializeAllServices();

      // Simulate a service failure
      globalServices.serviceStatus.cache.error = 'Test error';
      globalServices.serviceStatus.cache.initialized = false;

      // Test recovery
      await globalServices.recoverFailedServices();

      if (globalServices.serviceStatus.cache.error !== null) {
        throw new Error('Service error was not cleared during recovery');
      }

      if (!globalServices.serviceStatus.cache.initialized) {
        throw new Error('Service was not reinitialized during recovery');
      }
    });
  }

  private async testPerformance(): Promise<void> {
    await this.runTest('Performance', async () => {
      const globalServices = useGlobalServicesStore();
      
      const startTime = Date.now();
      await globalServices.initializeAllServices();
      const initTime = Date.now() - startTime;

      // Initialization should complete within 5 seconds
      if (initTime > 5000) {
        throw new Error(`Service initialization took too long: ${initTime}ms`);
      }

      // Test service access performance
      const accessStartTime = Date.now();
      for (let i = 0; i < 1000; i++) {
        const cache = globalServices.cacheService;
        const profile = globalServices.profileService;
        const activity = globalServices.activityService;
      }
      const accessTime = Date.now() - accessStartTime;

      // Service access should be fast
      if (accessTime > 100) {
        throw new Error(`Service access is too slow: ${accessTime}ms for 1000 accesses`);
      }
    });
  }

  private async testMemoryManagement(): Promise<void> {
    await this.runTest('Memory Management', async () => {
      const globalServices = useGlobalServicesStore();
      await globalServices.initializeAllServices();

      // Test that services are singletons
      const cache1 = globalServices.cacheService;
      const cache2 = globalServices.cacheService;
      
      if (cache1 !== cache2) {
        throw new Error('Cache service is not a singleton');
      }

      const profile1 = globalServices.profileService;
      const profile2 = globalServices.profileService;
      
      if (profile1 !== profile2) {
        throw new Error('Profile service is not a singleton');
      }

      // Test graceful shutdown
      await globalServices.shutdownAllServices();

      const allShutdown = Object.values(globalServices.serviceStatus)
        .every(status => !status.initialized && !status.initializing);

      if (!allShutdown) {
        throw new Error('Not all services were properly shut down');
      }
    });
  }

  private generateReport(): MigrationValidationReport {
    const totalTests = this.results.length;
    const passedTests = this.results.filter(r => r.passed).length;
    const failedTests = totalTests - passedTests;

    const overallStatus: 'PASSED' | 'FAILED' | 'PARTIAL' = 
      failedTests === 0 ? 'PASSED' :
      passedTests === 0 ? 'FAILED' : 'PARTIAL';

    const summary = `Migration validation ${overallStatus.toLowerCase()}: ${passedTests}/${totalTests} tests passed`;

    return {
      overallStatus,
      totalTests,
      passedTests,
      failedTests,
      results: this.results,
      summary
    };
  }
}

// Utility function for easy validation
export async function validateStateManagementMigration(): Promise<MigrationValidationReport> {
  const validator = new StateManagementMigrationValidator();
  return await validator.validateMigration();
}

// Console command for browser testing
if (typeof window !== 'undefined') {
  (window as any).__validateMigration = validateStateManagementMigration;
  console.log('🔧 Migration validation available: Use __validateMigration() in console');
}
