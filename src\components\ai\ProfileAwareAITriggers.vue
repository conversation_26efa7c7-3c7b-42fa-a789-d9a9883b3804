<template>
  <div class="profile-aware-ai-triggers">
    <!-- Matchmaking Section -->
    <q-card v-if="showMatchmaking" class="ai-trigger-card q-mb-md">
      <q-card-section class="bg-gradient-primary text-white">
        <div class="row items-center">
          <q-icon name="psychology" size="md" class="q-mr-sm" />
          <div>
            <div class="text-h6">AI-Powered Matchmaking</div>
            <div class="text-caption">Find your perfect connections</div>
          </div>
        </div>
      </q-card-section>
      
      <q-card-section>
        <div class="row q-col-gutter-sm">
          <div class="col-12 col-md-6" v-for="trigger in matchmakingTriggers" :key="trigger.key">
            <q-btn
              :color="trigger.color"
              :icon="trigger.icon"
              :label="trigger.label"
              class="full-width ai-trigger-btn"
              outline
              @click="handleAITrigger(trigger.key)"
            >
              <q-tooltip>{{ trigger.tooltip }}</q-tooltip>
            </q-btn>
          </div>
        </div>
      </q-card-section>
    </q-card>

    <!-- Profile Enhancement Section -->
    <q-card v-if="showProfileEnhancement" class="ai-trigger-card q-mb-md">
      <q-card-section class="bg-gradient-secondary text-white">
        <div class="row items-center">
          <q-icon name="person_add" size="md" class="q-mr-sm" />
          <div>
            <div class="text-h6">Profile Enhancement</div>
            <div class="text-caption">Optimize your profile with AI</div>
          </div>
        </div>
      </q-card-section>
      
      <q-card-section>
        <div class="row q-col-gutter-sm">
          <div class="col-12 col-md-6" v-for="trigger in profileTriggers" :key="trigger.key">
            <q-btn
              :color="trigger.color"
              :icon="trigger.icon"
              :label="trigger.label"
              class="full-width ai-trigger-btn"
              outline
              @click="handleAITrigger(trigger.key)"
            >
              <q-tooltip>{{ trigger.tooltip }}</q-tooltip>
            </q-btn>
          </div>
        </div>
      </q-card-section>
    </q-card>

    <!-- Quick AI Actions -->
    <q-card v-if="showQuickActions" class="ai-trigger-card">
      <q-card-section class="bg-gradient-accent text-white">
        <div class="row items-center">
          <q-icon name="auto_awesome" size="md" class="q-mr-sm" />
          <div>
            <div class="text-h6">Quick AI Assistance</div>
            <div class="text-caption">Get instant help</div>
          </div>
        </div>
      </q-card-section>
      
      <q-card-section>
        <div class="row q-col-gutter-sm">
          <div class="col-12 col-md-4" v-for="action in quickActions" :key="action.key">
            <q-btn
              :color="action.color"
              :icon="action.icon"
              :label="action.label"
              class="full-width ai-trigger-btn"
              size="sm"
              outline
              @click="handleAITrigger(action.key)"
            >
              <q-tooltip>{{ action.tooltip }}</q-tooltip>
            </q-btn>
          </div>
        </div>
      </q-card-section>
    </q-card>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useProfileStore } from '../../stores/profileStore';
import { useAuthStore } from '../../stores/auth';
import { useGlobalServicesStore } from '../../stores/globalServices';

interface AITrigger {
  key: string;
  label: string;
  icon: string;
  color: string;
  tooltip: string;
}

const props = defineProps<{
  context?: 'dashboard' | 'profile' | 'community' | 'general';
  showMatchmaking?: boolean;
  showProfileEnhancement?: boolean;
  showQuickActions?: boolean;
  profileType?: string;
}>();

const profileStore = useProfileStore();
const authStore = useAuthStore();
const globalServices = useGlobalServicesStore();

// Get current profile type
const currentProfileType = computed(() => {
  return props.profileType || profileStore.currentProfile?.profile_type || 'general';
});

// Matchmaking triggers based on profile type
const matchmakingTriggers = computed((): AITrigger[] => {
  const baseMatchmaking: AITrigger[] = [
    {
      key: 'find_matches',
      label: 'Find Matches',
      icon: 'people_alt',
      color: 'primary',
      tooltip: 'Find people and opportunities that match your profile'
    },
    {
      key: 'connection_strategy',
      label: 'Connection Strategy',
      icon: 'psychology',
      color: 'secondary',
      tooltip: 'Get AI advice on networking and building connections'
    }
  ];

  // Add profile-specific matchmaking triggers
  if (currentProfileType.value === 'innovator') {
    baseMatchmaking.push({
      key: 'find_investors',
      label: 'Find Investors',
      icon: 'attach_money',
      color: 'green',
      tooltip: 'Connect with potential investors for your projects'
    });
  } else if (currentProfileType.value === 'investor') {
    baseMatchmaking.push({
      key: 'discover_projects',
      label: 'Discover Projects',
      icon: 'lightbulb',
      color: 'orange',
      tooltip: 'Find promising innovation projects to invest in'
    });
  }

  return baseMatchmaking;
});

// Profile enhancement triggers
const profileTriggers = computed((): AITrigger[] => [
  {
    key: 'complete_profile',
    label: 'Complete Profile',
    icon: 'assignment_turned_in',
    color: 'blue',
    tooltip: 'Get guidance on completing your profile'
  },
  {
    key: 'profile_optimization',
    label: 'Optimize Profile',
    icon: 'tune',
    color: 'purple',
    tooltip: 'Optimize your profile for better visibility'
  },
  {
    key: 'skills_showcase',
    label: 'Showcase Skills',
    icon: 'star',
    color: 'amber',
    tooltip: 'Learn how to effectively showcase your skills'
  }
]);

// Quick action triggers
const quickActions = computed((): AITrigger[] => [
  {
    key: 'content_recommendations',
    label: 'Content Tips',
    icon: 'article',
    color: 'teal',
    tooltip: 'Get personalized content recommendations'
  },
  {
    key: 'networking',
    label: 'Networking',
    icon: 'group',
    color: 'indigo',
    tooltip: 'Get networking advice and opportunities'
  },
  {
    key: 'content_discovery',
    label: 'Discover',
    icon: 'explore',
    color: 'cyan',
    tooltip: 'Discover relevant content and discussions'
  }
]);

// Handle AI trigger clicks
const handleAITrigger = async (triggerKey: string) => {
  try {
    await globalServices.aiChatTriggerService.triggerChat(triggerKey, currentProfileType.value);
  } catch (error) {
    console.error('Error triggering AI chat:', error);
  }
};
</script>

<style scoped>
.profile-aware-ai-triggers {
  width: 100%;
}

.ai-trigger-card {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.ai-trigger-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.ai-trigger-btn {
  border-radius: 8px;
  font-weight: 500;
  text-transform: none;
  padding: 8px 16px;
  transition: all 0.2s ease;
}

.ai-trigger-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.bg-gradient-primary {
  background: linear-gradient(135deg, #1976d2 0%, #42a5f5 100%);
}

.bg-gradient-secondary {
  background: linear-gradient(135deg, #7b1fa2 0%, #ba68c8 100%);
}

.bg-gradient-accent {
  background: linear-gradient(135deg, #388e3c 0%, #66bb6a 100%);
}
</style>
