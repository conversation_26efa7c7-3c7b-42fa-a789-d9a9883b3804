<template>
  <section class="success-stories-section q-py-xl">
    <div class="container q-mx-auto q-px-md">
      <div class="row">
        <div class="col-1" />
        <div class="col-10">
          <div class="text-center">
            <h2 class="text-h3 text-weight-light q-mb-md">Success Stories</h2>
            <p class="text-body1 q-mb-xl text-center">
              See how innovators and entrepreneurs have leveraged our platform to achieve remarkable growth and impact.
            </p>
          </div>

          <!-- Loading State -->
          <div v-if="loading" class="text-center q-pa-xl">
            <q-spinner color="primary" size="3em" />
            <p>Loading success stories...</p>
          </div>

          <!-- Error State -->
          <div v-else-if="error" class="text-center q-pa-xl text-negative">
            <q-icon name="error" size="3em" />
            <p>{{ error }}</p>
          </div>

          <!-- Empty State -->
          <div v-else-if="successStories.length === 0" class="text-center q-pa-xl">
            <q-icon name="info" size="3em" color="grey" />
            <p>No success stories found.</p>
          </div>

          <!-- Success Stories Carousel -->
          <div v-else class="carousel-container">
            <q-carousel
              v-model="slide"
              animated
              navigation
              arrows
              control-color="primary"
              class="success-carousel"
              height="500px"
            >
              <q-carousel-slide v-for="(story, index) in successStories" :key="index" :name="index" class="story-slide">
                <div class="row items-center">
                  <div class="col-md-6 col-sm-12">
                    <div class="story-image-container">
                      <div class="story-image" :style="{ backgroundImage: `url(${story.image || 'https://placehold.co/600x400?text=No+Image'})` }">
                        <div class="image-overlay"></div>
                      </div>
                      <div class="floating-badge" :class="`bg-${story.badgeColor || 'primary'}`">
                        {{ story.category || 'Success Story' }}
                      </div>
                    </div>
                  </div>
                  <div class="col-md-6 col-sm-12">
                    <div class="story-content q-pa-md">
                      <div class="story-quote">"</div>
                      <h3 class="text-h5 q-mb-md">{{ story.title || 'Untitled Story' }}</h3>
                      <p class="text-body1 q-mb-lg">{{ story.description || 'No description available' }}</p>
                      <div class="author-info row items-center">
                        <q-avatar size="50px">
                          <img :src="story.authorAvatar || 'https://cdn.quasar.dev/img/avatar.png'" alt="Author" />
                        </q-avatar>
                        <div class="q-ml-md">
                          <div class="text-weight-bold">{{ story.authorName || 'Anonymous' }}</div>
                          <div class="text-caption">{{ story.authorRole || 'Community Member' }}</div>
                        </div>
                      </div>
                      <div v-if="story.stats && story.stats.length" class="story-stats row q-mt-lg">
                        <div v-for="(stat, statIndex) in story.stats" :key="statIndex" class="col-4 text-center">
                          <div class="text-h5 text-primary">{{ stat.value }}</div>
                          <div class="text-caption">{{ stat.label }}</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </q-carousel-slide>
            </q-carousel>
          </div>

          <div class="text-center q-mt-xl">
            <q-btn
              outline
              color="primary"
              :label="isAuthenticated ? 'Share Your Story' : 'Log In to Share Your Story'"
              :icon-right="isAuthenticated ? 'edit' : 'login'"
              no-caps
              class="q-px-xl"
              rounded
              @click="openStoryForm"
            />
          </div>
        </div>
        <div class="col-1" />
      </div>
    </div>

    <!-- Story Submission Dialog -->
    <q-dialog v-model="storyFormOpen">
      <q-card style="width: 700px; max-width: 90vw;">
        <q-card-section class="bg-primary text-white">
          <div class="text-h6">Share Your Success Story</div>
        </q-card-section>

        <q-card-section>
          <p class="text-body1">We'd love to hear how the Innovation Hub has helped your journey. Submit your story for a chance to be featured on our platform.</p>

          <q-form @submit.prevent="submitStory" class="q-gutter-md q-mt-md">
            <q-input
              v-model="storyForm.name"
              label="Your Name *"
              outlined
              dense
              :rules="[val => !!val || 'Name is required']"
            />
            <q-input
              v-model="storyForm.email"
              label="Email Address *"
              outlined
              dense
              type="email"
              :rules="[
                val => !!val || 'Email is required',
                val => /^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/.test(val) || 'Please enter a valid email'
              ]"
            />
            <q-input
              v-model="storyForm.company"
              label="Company/Organization"
              outlined
              dense
            />
            <q-input
              v-model="storyForm.title"
              label="Story Title *"
              outlined
              dense
              :rules="[val => !!val || 'Title is required']"
            />
            <q-input
              v-model="storyForm.story"
              label="Your Success Story *"
              outlined
              type="textarea"
              rows="5"
              :rules="[val => !!val || 'Story content is required']"
            />

            <div class="text-caption text-grey q-mb-md">* Required fields</div>

            <div class="row justify-end q-mt-md">
              <q-btn flat label="Cancel" color="grey" v-close-popup :disable="submitting" />
              <q-btn
                unelevated
                label="Submit"
                color="primary"
                type="submit"
                class="q-ml-sm"
                :loading="submitting"
                :disable="!storyForm.name || !storyForm.email || !storyForm.title || !storyForm.story"
              />
            </div>
          </q-form>
        </q-card-section>
      </q-card>
    </q-dialog>
  </section>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import { useRouter } from 'vue-router';
import { useSuccessStoriesStore } from '../../stores/successStories';
import { useAuthStore } from '../../stores/auth';
import { useQuasar } from 'quasar';
import { useGlobalServicesStore } from '../../stores/globalServices';

const successStoriesStore = useSuccessStoriesStore();
const authStore = useAuthStore();
const $q = useQuasar();
const globalServices = useGlobalServicesStore();
const activityService = globalServices.activityService;
const router = useRouter();

const slide = ref(0);
const storyFormOpen = ref(false);
const submitting = ref(false);
const storyForm = ref({
  name: '',
  email: '',
  company: '',
  title: '',
  story: ''
});

// Use computed properties to access store data
const successStories = computed(() => successStoriesStore.stories);
const loading = computed(() => successStoriesStore.loading);
const error = ref(null);
const isAuthenticated = computed(() => authStore.isAuthenticated);

// Load success stories on component mount
onMounted(async () => {
  try {
    error.value = null;
    await successStoriesStore.fetchSuccessStories();

    // Pre-fill form with user data if authenticated
    if (isAuthenticated.value && authStore.currentUser) {
      storyForm.value.name = `${authStore.currentUser.first_name || ''} ${authStore.currentUser.last_name || ''}`.trim();
      storyForm.value.email = authStore.currentUser.email || '';
    }
  } catch (err) {
    console.error('Error loading success stories:', err);
    error.value = 'Failed to load success stories';
  }
});

const openStoryForm = () => {
  // Check if user is authenticated
  if (isAuthenticated.value) {
    storyFormOpen.value = true;
  } else {
    // Show login dialog
    $q.dialog({
      title: 'Authentication Required',
      message: 'Please log in or register to share your success story.',
      persistent: true,
      ok: {
        label: 'Log In',
        color: 'primary',
        flat: false,
        unelevated: true
      },
      cancel: {
        label: 'Cancel',
        color: 'grey',
        flat: true
      }
    }).onOk(() => {
      // Redirect to login page with return URL
      const currentPath = window.location.pathname;
      router.push({
        name: 'login',
        query: { redirect: currentPath + '#success-stories' }
      });
    });
  }
};

const submitStory = async () => {
  if (!storyForm.value.name || !storyForm.value.email || !storyForm.value.title || !storyForm.value.story) {
    $q.notify({
      color: 'negative',
      message: 'Please fill in all required fields',
      icon: 'warning'
    });
    return;
  }

  submitting.value = true;

  try {
    // Submit the story using the store
    const result = await successStoriesStore.submitStory({
      name: storyForm.value.name,
      email: storyForm.value.email,
      company: storyForm.value.company,
      title: storyForm.value.title,
      description: storyForm.value.story
    });

    if (result) {
      // Track activity if user is authenticated
      if (isAuthenticated.value) {
        await activityService.trackActivity('success_story_submit', {
          title: storyForm.value.title,
          content_preview: storyForm.value.story.substring(0, 100)
        });
      }

      // Show success message
      $q.notify({
        color: 'positive',
        message: 'Thank you for sharing your story! Our team will review it shortly.',
        icon: 'check_circle'
      });

      // Reset form and close dialog
      storyForm.value = {
        name: '',
        email: '',
        company: '',
        title: '',
        story: ''
      };
      storyFormOpen.value = false;
    }
  } catch (err) {
    console.error('Error submitting story:', err);
    $q.notify({
      color: 'negative',
      message: 'There was an error submitting your story. Please try again later.',
      icon: 'error'
    });
  } finally {
    submitting.value = false;
  }
};
</script>

<style scoped>
.success-stories-section {
  background-color: #f5f5f5;
  padding: 80px 0;
  position: relative;
  overflow: hidden;
}

.container {
  width: 100%;
  max-width: 1400px;
}

.carousel-container {
  margin: 40px 0;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  border-radius: 12px;
  overflow: hidden;
}

.success-carousel {
  background-color: white;
}

.story-slide {
  padding: 0;
}

.story-image-container {
  position: relative;
  height: 500px;
  overflow: hidden;
}

.story-image {
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  transition: transform 0.5s ease;
}

.story-slide:hover .story-image {
  transform: scale(1.05);
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to right, rgba(0,0,0,0.1), rgba(0,0,0,0.4));
}

.floating-badge {
  position: absolute;
  top: 20px;
  left: 20px;
  padding: 8px 16px;
  border-radius: 20px;
  color: white;
  font-weight: 500;
  font-size: 0.9rem;
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.2);
  z-index: 2;
}

.story-content {
  position: relative;
  padding: 30px;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.story-quote {
  position: absolute;
  top: 20px;
  left: 20px;
  font-size: 6rem;
  color: rgba(13, 138, 62, 0.1);
  font-family: Georgia, serif;
  line-height: 1;
}

.author-info {
  margin-top: auto;
  padding-top: 20px;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.story-stats {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px dashed rgba(0, 0, 0, 0.1);
}

@media (max-width: 767px) {
  .story-image-container {
    height: 200px;
  }

  .story-content {
    padding: 20px;
  }

  .story-quote {
    font-size: 4rem;
    top: 10px;
    left: 10px;
  }

  .floating-badge {
    top: 10px;
    left: 10px;
    padding: 5px 10px;
    font-size: 0.8rem;
  }

  .success-carousel {
    height: auto !important;
  }
}
</style>
