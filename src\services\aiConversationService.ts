import { supabase } from '../lib/supabase';
import type { EnhancedChatMessage } from './aiEnhancedService';

/**
 * Generate text embedding using Supabase Edge Function
 * This avoids direct API calls and uses proper state management
 */
async function generateEmbedding(text: string): Promise<number[]> {
  try {
    const { data, error } = await supabase.functions.invoke('generate-embedding', {
      body: { text: text.substring(0, 8000) }
    });

    if (error) {
      throw new Error(`Embedding service error: ${error.message}`);
    }

    return data?.embedding || new Array(1536).fill(0);
  } catch (error) {
    console.error('Error generating embedding:', error);
    // Return a zero vector as fallback
    return new Array(1536).fill(0);
  }
}

export interface AIConversation {
  id: string;
  user_id: string;
  title?: string;
  summary?: string;
  summary_embedding?: number[];
  context_data: Record<string, any>;
  message_count: number;
  last_message_at?: string;
  created_at: string;
  updated_at: string;
}

export interface AIMessage {
  id: string;
  conversation_id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  content_embedding?: number[];
  metadata: Record<string, any>;
  actions: any[];
  suggestions: any[];
  created_at: string;
}

export interface ConversationSearchResult {
  id: string;
  title?: string;
  summary?: string;
  similarity: number;
  message_count: number;
  last_message_at?: string;
  created_at: string;
}

export interface MessageSearchResult {
  id: string;
  conversation_id: string;
  role: string;
  content: string;
  similarity: number;
  created_at: string;
}

/**
 * AI Conversation Persistence Service
 * Handles saving, retrieving, and searching AI conversations with vector embeddings
 */
export class AIConversationService {
  
  /**
   * Create a new conversation
   */
  static async createConversation(
    userId: string,
    title?: string,
    contextData: Record<string, any> = {}
  ): Promise<AIConversation> {
    const { data, error } = await supabase
      .from('ai_conversations')
      .insert({
        user_id: userId,
        title,
        context_data: contextData
      })
      .select()
      .single();

    if (error) {
      console.error('Error creating conversation:', error);
      throw new Error(`Failed to create conversation: ${error.message}`);
    }

    return data;
  }

  /**
   * Get conversation by ID
   */
  static async getConversation(conversationId: string): Promise<AIConversation | null> {
    const { data, error } = await supabase
      .from('ai_conversations')
      .select('*')
      .eq('id', conversationId)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return null; // Not found
      }
      console.error('Error getting conversation:', error);
      throw new Error(`Failed to get conversation: ${error.message}`);
    }

    return data;
  }

  /**
   * Get user's conversations with pagination
   */
  static async getUserConversations(
    userId: string,
    limit: number = 20,
    offset: number = 0
  ): Promise<AIConversation[]> {
    const { data, error } = await supabase
      .from('ai_conversations')
      .select('*')
      .eq('user_id', userId)
      .order('last_message_at', { ascending: false, nullsFirst: false })
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (error) {
      console.error('Error getting user conversations:', error);
      throw new Error(`Failed to get conversations: ${error.message}`);
    }

    return data || [];
  }

  /**
   * Update conversation title and summary
   */
  static async updateConversation(
    conversationId: string,
    updates: {
      title?: string;
      summary?: string;
      summary_embedding?: number[];
      context_data?: Record<string, any>;
    }
  ): Promise<AIConversation> {
    const { data, error } = await supabase
      .from('ai_conversations')
      .update(updates)
      .eq('id', conversationId)
      .select()
      .single();

    if (error) {
      console.error('Error updating conversation:', error);
      throw new Error(`Failed to update conversation: ${error.message}`);
    }

    return data;
  }

  /**
   * Delete conversation and all its messages
   */
  static async deleteConversation(conversationId: string): Promise<void> {
    const { error } = await supabase
      .from('ai_conversations')
      .delete()
      .eq('id', conversationId);

    if (error) {
      console.error('Error deleting conversation:', error);
      throw new Error(`Failed to delete conversation: ${error.message}`);
    }
  }

  /**
   * Add message to conversation
   */
  static async addMessage(
    conversationId: string,
    role: 'user' | 'assistant' | 'system',
    content: string,
    options: {
      content_embedding?: number[];
      metadata?: Record<string, any>;
      actions?: any[];
      suggestions?: any[];
    } = {}
  ): Promise<AIMessage> {
    const { data, error } = await supabase
      .from('ai_messages')
      .insert({
        conversation_id: conversationId,
        role,
        content,
        content_embedding: options.content_embedding,
        metadata: options.metadata || {},
        actions: options.actions || [],
        suggestions: options.suggestions || []
      })
      .select()
      .single();

    if (error) {
      console.error('Error adding message:', error);
      throw new Error(`Failed to add message: ${error.message}`);
    }

    return data;
  }

  /**
   * Get conversation messages with pagination
   */
  static async getConversationMessages(
    conversationId: string,
    limit: number = 50,
    offset: number = 0
  ): Promise<AIMessage[]> {
    const { data, error } = await supabase
      .from('ai_messages')
      .select('*')
      .eq('conversation_id', conversationId)
      .order('created_at', { ascending: true })
      .range(offset, offset + limit - 1);

    if (error) {
      console.error('Error getting conversation messages:', error);
      throw new Error(`Failed to get messages: ${error.message}`);
    }

    return data || [];
  }

  /**
   * Get recent conversation context (last N messages)
   */
  static async getConversationContext(
    conversationId: string,
    messageLimit: number = 10
  ): Promise<AIMessage[]> {
    const { data, error } = await supabase
      .rpc('get_conversation_context', {
        target_conversation_id: conversationId,
        message_limit: messageLimit
      });

    if (error) {
      console.error('Error getting conversation context:', error);
      throw new Error(`Failed to get conversation context: ${error.message}`);
    }

    return data || [];
  }

  /**
   * Search conversations using vector similarity
   */
  static async searchConversations(
    queryEmbedding: number[],
    userId?: string,
    options: {
      matchThreshold?: number;
      matchCount?: number;
    } = {}
  ): Promise<ConversationSearchResult[]> {
    const { data, error } = await supabase
      .rpc('search_conversations', {
        query_embedding: queryEmbedding,
        match_threshold: options.matchThreshold || 0.8,
        match_count: options.matchCount || 10,
        target_user_id: userId
      });

    if (error) {
      console.error('Error searching conversations:', error);
      throw new Error(`Failed to search conversations: ${error.message}`);
    }

    return data || [];
  }

  /**
   * Search messages using vector similarity
   */
  static async searchMessages(
    queryEmbedding: number[],
    options: {
      userId?: string;
      conversationId?: string;
      matchThreshold?: number;
      matchCount?: number;
    } = {}
  ): Promise<MessageSearchResult[]> {
    const { data, error } = await supabase
      .rpc('search_messages', {
        query_embedding: queryEmbedding,
        match_threshold: options.matchThreshold || 0.8,
        match_count: options.matchCount || 20,
        target_user_id: options.userId,
        target_conversation_id: options.conversationId
      });

    if (error) {
      console.error('Error searching messages:', error);
      throw new Error(`Failed to search messages: ${error.message}`);
    }

    return data || [];
  }

  /**
   * Generate conversation title from first few messages
   */
  static async generateConversationTitle(conversationId: string): Promise<string> {
    const messages = await this.getConversationMessages(conversationId, 3);
    
    if (messages.length === 0) {
      return 'New Conversation';
    }

    // Get the first user message
    const firstUserMessage = messages.find(m => m.role === 'user');
    if (!firstUserMessage) {
      return 'New Conversation';
    }

    // Create a simple title from the first user message
    const content = firstUserMessage.content.trim();
    if (content.length <= 50) {
      return content;
    }

    // Truncate and add ellipsis
    return content.substring(0, 47) + '...';
  }

  /**
   * Save enhanced chat message to conversation with automatic embedding generation
   */
  static async saveEnhancedMessage(
    conversationId: string,
    message: EnhancedChatMessage,
    embedding?: number[]
  ): Promise<AIMessage> {
    // Generate embedding if not provided
    const contentEmbedding = embedding || await generateEmbedding(message.content);

    return this.addMessage(
      conversationId,
      message.role,
      message.content,
      {
        content_embedding: contentEmbedding,
        metadata: {
          timestamp: message.timestamp,
          user_context: message.user_context
        },
        actions: message.actions || [],
        suggestions: message.suggestions || []
      }
    );
  }

  /**
   * Add message with automatic embedding generation
   */
  static async addMessageWithEmbedding(
    conversationId: string,
    role: 'user' | 'assistant' | 'system',
    content: string,
    options: {
      metadata?: Record<string, any>;
      actions?: any[];
      suggestions?: any[];
    } = {}
  ): Promise<AIMessage> {
    const embedding = await generateEmbedding(content);

    return this.addMessage(conversationId, role, content, {
      content_embedding: embedding,
      ...options
    });
  }

  /**
   * Update conversation summary with automatic embedding generation
   */
  static async updateConversationSummary(
    conversationId: string,
    summary: string,
    title?: string
  ): Promise<AIConversation> {
    const summaryEmbedding = await generateEmbedding(summary);

    return this.updateConversation(conversationId, {
      title,
      summary,
      summary_embedding: summaryEmbedding
    });
  }

  /**
   * Search conversations by text query (generates embedding automatically)
   */
  static async searchConversationsByText(
    query: string,
    userId?: string,
    options: {
      matchThreshold?: number;
      matchCount?: number;
    } = {}
  ): Promise<ConversationSearchResult[]> {
    const queryEmbedding = await generateEmbedding(query);
    return this.searchConversations(queryEmbedding, userId, options);
  }

  /**
   * Search messages by text query (generates embedding automatically)
   */
  static async searchMessagesByText(
    query: string,
    options: {
      userId?: string;
      conversationId?: string;
      matchThreshold?: number;
      matchCount?: number;
    } = {}
  ): Promise<MessageSearchResult[]> {
    const queryEmbedding = await generateEmbedding(query);
    return this.searchMessages(queryEmbedding, options);
  }

  /**
   * Get or create conversation for user
   * Useful for maintaining conversation continuity
   */
  static async getOrCreateUserConversation(
    userId: string,
    contextData: Record<string, any> = {}
  ): Promise<AIConversation> {
    // Try to get the most recent conversation
    const conversations = await this.getUserConversations(userId, 1);

    if (conversations.length > 0) {
      const lastConversation = conversations[0];

      // If the last conversation is recent (within 24 hours), use it
      const lastMessageTime = lastConversation.last_message_at
        ? new Date(lastConversation.last_message_at)
        : new Date(lastConversation.created_at);

      const hoursSinceLastMessage = (Date.now() - lastMessageTime.getTime()) / (1000 * 60 * 60);

      if (hoursSinceLastMessage < 24) {
        return lastConversation;
      }
    }

    // Create a new conversation
    return this.createConversation(userId, undefined, contextData);
  }
}
