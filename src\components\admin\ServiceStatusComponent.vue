<template>
  <div class="service-status-component">
    <!-- Header -->
    <div class="row items-center justify-between q-mb-md">
      <div>
        <h5 class="text-h5 q-my-none">Service Status Dashboard</h5>
        <p class="text-caption text-grey-6 q-mt-xs">
          Monitor and manage application services
        </p>
      </div>
      <div class="row q-gutter-sm">
        <q-btn
          @click="refreshStatus"
          :loading="refreshing"
          icon="refresh"
          label="Refresh"
          color="primary"
          outline
          size="sm"
        />
        <q-btn
          @click="runDiagnostics"
          :loading="runningDiagnostics"
          icon="bug_report"
          label="Diagnostics"
          color="secondary"
          outline
          size="sm"
        />
      </div>
    </div>

    <!-- Overall Health Status -->
    <q-card class="q-mb-md">
      <q-card-section>
        <div class="row items-center q-gutter-md">
          <q-circular-progress
            :value="healthPercentage"
            size="80px"
            :thickness="0.15"
            :color="healthColor"
            track-color="grey-3"
            class="q-ma-md"
          >
            <div class="text-h6" :class="`text-${healthColor}`">
              {{ Math.round(healthPercentage) }}%
            </div>
          </q-circular-progress>
          
          <div class="col">
            <div class="text-h6" :class="`text-${healthColor}`">
              {{ healthStatus }}
            </div>
            <div class="text-body2 text-grey-6">
              {{ healthyServices }}/{{ totalServices }} services healthy
            </div>
            <div v-if="failedServices.length > 0" class="text-caption text-negative">
              Failed: {{ failedServices.map(s => s.name).join(', ') }}
            </div>
          </div>

          <div class="row q-gutter-sm">
            <q-btn
              v-if="healthStatus !== 'HEALTHY'"
              @click="recoverServices"
              :loading="recovering"
              icon="healing"
              label="Recover"
              color="positive"
              size="sm"
            />
            <q-btn
              @click="resetAllServices"
              :loading="resetting"
              icon="restart_alt"
              label="Reset All"
              color="warning"
              size="sm"
            />
          </div>
        </div>
      </q-card-section>
    </q-card>

    <!-- Individual Service Status -->
    <q-card>
      <q-card-section>
        <div class="text-h6 q-mb-md">Individual Services</div>
        
        <div class="row q-col-gutter-md">
          <div
            v-for="service in serviceList"
            :key="service.name"
            class="col-12 col-md-6 col-lg-4"
          >
            <q-card
              flat
              bordered
              :class="`service-card ${service.statusClass}`"
            >
              <q-card-section class="q-pa-md">
                <div class="row items-center justify-between">
                  <div class="col">
                    <div class="text-subtitle1 text-weight-medium">
                      {{ service.name }}
                    </div>
                    <div class="text-caption text-grey-6">
                      {{ service.description }}
                    </div>
                  </div>
                  <q-icon
                    :name="service.icon"
                    :color="service.iconColor"
                    size="24px"
                  />
                </div>

                <div class="q-mt-sm">
                  <q-chip
                    :color="service.statusColor"
                    text-color="white"
                    size="sm"
                    :label="service.status"
                  />
                  
                  <div v-if="service.lastInitialized" class="text-caption text-grey-6 q-mt-xs">
                    Last initialized: {{ formatTime(service.lastInitialized) }}
                  </div>
                  
                  <div v-if="service.error" class="text-caption text-negative q-mt-xs">
                    Error: {{ service.error }}
                  </div>
                </div>

                <div v-if="service.metrics" class="q-mt-sm">
                  <div class="text-caption text-grey-6">
                    <div v-if="service.metrics.hitRate !== undefined">
                      Hit Rate: {{ (service.metrics.hitRate * 100).toFixed(1) }}%
                    </div>
                    <div v-if="service.metrics.subscriptions !== undefined">
                      Subscriptions: {{ service.metrics.subscriptions }}
                    </div>
                    <div v-if="service.metrics.memoryUsage !== undefined">
                      Memory: {{ formatBytes(service.metrics.memoryUsage) }}
                    </div>
                  </div>
                </div>

                <div class="row q-gutter-xs q-mt-sm">
                  <q-btn
                    v-if="service.status === 'Error'"
                    @click="recoverService(service.key)"
                    :loading="service.recovering"
                    icon="healing"
                    size="xs"
                    color="positive"
                    dense
                    flat
                  >
                    Recover
                  </q-btn>
                  <q-btn
                    @click="reinitializeService(service.key)"
                    :loading="service.reinitializing"
                    icon="refresh"
                    size="xs"
                    color="primary"
                    dense
                    flat
                  >
                    Reinit
                  </q-btn>
                </div>
              </q-card-section>
            </q-card>
          </div>
        </div>
      </q-card-section>
    </q-card>

    <!-- Performance Metrics -->
    <q-card class="q-mt-md" v-if="performanceMetrics">
      <q-card-section>
        <div class="text-h6 q-mb-md">Performance Metrics</div>
        
        <div class="row q-col-gutter-md">
          <div class="col-12 col-md-3">
            <q-stat
              label="Initialization Time"
              :value="`${performanceMetrics.serviceInitializationTime}ms`"
              color="primary"
              icon="timer"
            />
          </div>
          <div class="col-12 col-md-3">
            <q-stat
              label="Cache Hit Rate"
              :value="`${(performanceMetrics.cacheHitRate * 100).toFixed(1)}%`"
              color="positive"
              icon="storage"
            />
          </div>
          <div class="col-12 col-md-3">
            <q-stat
              label="Memory Usage"
              :value="formatBytes(performanceMetrics.memoryUsage)"
              color="info"
              icon="memory"
            />
          </div>
          <div class="col-12 col-md-3">
            <q-stat
              label="Active Subscriptions"
              :value="performanceMetrics.activeSubscriptions"
              color="secondary"
              icon="wifi"
            />
          </div>
        </div>
      </q-card-section>
    </q-card>

    <!-- Error Statistics -->
    <q-card class="q-mt-md" v-if="errorStats">
      <q-card-section>
        <div class="text-h6 q-mb-md">Error Statistics</div>
        
        <div class="row q-col-gutter-md">
          <div class="col-12 col-md-4">
            <div class="text-h4 text-negative">{{ errorStats.total }}</div>
            <div class="text-caption text-grey-6">Total Errors</div>
          </div>
          <div class="col-12 col-md-4">
            <div class="text-h4 text-positive">{{ (errorStats.recoveryRate * 100).toFixed(1) }}%</div>
            <div class="text-caption text-grey-6">Recovery Rate</div>
          </div>
          <div class="col-12 col-md-4">
            <div class="text-body1">
              <div v-for="(count, type) in errorStats.byType" :key="type" class="q-mb-xs">
                <q-chip size="sm" :label="`${type}: ${count}`" />
              </div>
            </div>
          </div>
        </div>
      </q-card-section>
    </q-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { useGlobalServicesStore } from '../../stores/globalServices';
import { useQuasar } from 'quasar';

// Composables
const $q = useQuasar();
const globalServices = useGlobalServicesStore();

// State
const refreshing = ref(false);
const runningDiagnostics = ref(false);
const recovering = ref(false);
const resetting = ref(false);
const performanceMetrics = ref<any>(null);
const errorStats = ref<any>(null);
const autoRefreshInterval = ref<NodeJS.Timeout | null>(null);

// Service recovery states
const serviceRecoveryStates = ref<Record<string, boolean>>({});
const serviceReinitStates = ref<Record<string, boolean>>({});

// Computed properties
const healthStatus = computed(() => globalServices.serviceHealth?.status || 'UNKNOWN');
const healthPercentage = computed(() => globalServices.serviceHealth?.healthPercentage || 0);
const healthyServices = computed(() => globalServices.serviceHealth?.healthy || 0);
const totalServices = computed(() => globalServices.serviceHealth?.total || 0);
const failedServices = computed(() => globalServices.serviceHealth?.failedServices || []);

const healthColor = computed(() => {
  switch (healthStatus.value) {
    case 'HEALTHY': return 'positive';
    case 'DEGRADED': return 'warning';
    case 'UNHEALTHY': return 'negative';
    default: return 'grey';
  }
});

const serviceList = computed(() => {
  const services = [
    { key: 'cache', name: 'Cache Service', description: 'Unified caching system' },
    { key: 'realtime', name: 'Realtime Service', description: 'Real-time subscriptions' },
    { key: 'activity', name: 'Activity Service', description: 'Activity tracking' },
    { key: 'connection', name: 'Connection Service', description: 'User connections' },
    { key: 'profile', name: 'Profile Service', description: 'Profile management' },
    { key: 'profileManager', name: 'Profile Manager', description: 'Profile coordination' },
    { key: 'matchmaking', name: 'Matchmaking Service', description: 'Content matching' },
    { key: 'aiChatTrigger', name: 'AI Chat Trigger', description: 'AI chat triggers' },
    { key: 'aiEnhanced', name: 'AI Enhanced Service', description: 'Enhanced AI features' }
  ];

  return services.map(service => {
    const status = globalServices.serviceStatus[service.key as keyof typeof globalServices.serviceStatus];
    const isError = !!status?.error;
    const isInitializing = !!status?.initializing;
    const isInitialized = !!status?.initialized;

    let statusText = 'Unknown';
    let statusColor = 'grey';
    let statusClass = 'service-unknown';
    let icon = 'help';
    let iconColor = 'grey';

    if (isError) {
      statusText = 'Error';
      statusColor = 'negative';
      statusClass = 'service-error';
      icon = 'error';
      iconColor = 'negative';
    } else if (isInitializing) {
      statusText = 'Initializing';
      statusColor = 'warning';
      statusClass = 'service-initializing';
      icon = 'hourglass_empty';
      iconColor = 'warning';
    } else if (isInitialized) {
      statusText = 'Healthy';
      statusColor = 'positive';
      statusClass = 'service-healthy';
      icon = 'check_circle';
      iconColor = 'positive';
    }

    return {
      ...service,
      status: statusText,
      statusColor,
      statusClass,
      icon,
      iconColor,
      error: status?.error,
      lastInitialized: status?.lastInitialized,
      recovering: serviceRecoveryStates.value[service.key] || false,
      reinitializing: serviceReinitStates.value[service.key] || false,
      metrics: getServiceMetrics(service.key)
    };
  });
});

// Methods
function getServiceMetrics(serviceKey: string) {
  // Return service-specific metrics if available
  if (serviceKey === 'cache' && globalServices.cacheService) {
    try {
      const stats = (globalServices.cacheService as any).getStats?.();
      return stats ? { hitRate: stats.hitRate, memoryUsage: stats.memoryUsage } : null;
    } catch (e) {
      return null;
    }
  }
  
  if (serviceKey === 'realtime' && globalServices.realtimeService) {
    try {
      const stats = (globalServices.realtimeService as any).getStats?.();
      return stats ? { subscriptions: stats.activeSubscriptions } : null;
    } catch (e) {
      return null;
    }
  }
  
  return null;
}

async function refreshStatus() {
  refreshing.value = true;
  try {
    // Force refresh of service status
    await globalServices.initializeAllServices();
    
    // Update performance metrics
    await updatePerformanceMetrics();
    
    $q.notify({
      type: 'positive',
      message: 'Service status refreshed',
      timeout: 2000
    });
  } catch (error: any) {
    $q.notify({
      type: 'negative',
      message: `Failed to refresh status: ${error.message}`,
      timeout: 3000
    });
  } finally {
    refreshing.value = false;
  }
}

async function runDiagnostics() {
  runningDiagnostics.value = true;
  try {
    // Run comprehensive diagnostics
    if (typeof (window as any).__SERVICE_DIAGNOSTICS__ === 'function') {
      const report = await (window as any).__SERVICE_DIAGNOSTICS__();
      console.log('Diagnostics Report:', report);
      
      $q.notify({
        type: 'info',
        message: 'Diagnostics completed. Check console for details.',
        timeout: 3000
      });
    } else {
      $q.notify({
        type: 'warning',
        message: 'Diagnostics not available in this environment',
        timeout: 3000
      });
    }
  } catch (error: any) {
    $q.notify({
      type: 'negative',
      message: `Diagnostics failed: ${error.message}`,
      timeout: 3000
    });
  } finally {
    runningDiagnostics.value = false;
  }
}

async function recoverServices() {
  recovering.value = true;
  try {
    await globalServices.recoverFailedServices();
    
    $q.notify({
      type: 'positive',
      message: 'Service recovery completed',
      timeout: 2000
    });
  } catch (error: any) {
    $q.notify({
      type: 'negative',
      message: `Recovery failed: ${error.message}`,
      timeout: 3000
    });
  } finally {
    recovering.value = false;
  }
}

async function resetAllServices() {
  resetting.value = true;
  try {
    await globalServices.shutdownAllServices();
    await globalServices.initializeAllServices();
    
    $q.notify({
      type: 'positive',
      message: 'All services reset successfully',
      timeout: 2000
    });
  } catch (error: any) {
    $q.notify({
      type: 'negative',
      message: `Reset failed: ${error.message}`,
      timeout: 3000
    });
  } finally {
    resetting.value = false;
  }
}

async function recoverService(serviceKey: string) {
  serviceRecoveryStates.value[serviceKey] = true;
  try {
    // Implement individual service recovery
    await globalServices.recoverFailedServices();
    
    $q.notify({
      type: 'positive',
      message: `${serviceKey} service recovered`,
      timeout: 2000
    });
  } catch (error: any) {
    $q.notify({
      type: 'negative',
      message: `Failed to recover ${serviceKey}: ${error.message}`,
      timeout: 3000
    });
  } finally {
    serviceRecoveryStates.value[serviceKey] = false;
  }
}

async function reinitializeService(serviceKey: string) {
  serviceReinitStates.value[serviceKey] = true;
  try {
    // Implement individual service reinitialization
    const initMethod = `initialize${serviceKey.charAt(0).toUpperCase() + serviceKey.slice(1)}Service`;
    if (typeof (globalServices as any)[initMethod] === 'function') {
      await (globalServices as any)[initMethod]();
    } else {
      await globalServices.initializeAllServices();
    }
    
    $q.notify({
      type: 'positive',
      message: `${serviceKey} service reinitialized`,
      timeout: 2000
    });
  } catch (error: any) {
    $q.notify({
      type: 'negative',
      message: `Failed to reinitialize ${serviceKey}: ${error.message}`,
      timeout: 3000
    });
  } finally {
    serviceReinitStates.value[serviceKey] = false;
  }
}

async function updatePerformanceMetrics() {
  try {
    if (typeof (window as any).__SERVICE_DEV_TOOLS__ === 'object') {
      const devTools = (window as any).__SERVICE_DEV_TOOLS__;
      performanceMetrics.value = await devTools.getPerformanceMetrics();
    }
    
    if (typeof (window as any).__ERROR_HANDLER__ === 'object') {
      const errorHandler = (window as any).__ERROR_HANDLER__;
      errorStats.value = errorHandler.getErrorStatistics();
    }
  } catch (error) {
    console.warn('Failed to update performance metrics:', error);
  }
}

function formatTime(timestamp: number | string) {
  return new Date(timestamp).toLocaleTimeString();
}

function formatBytes(bytes: number) {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function startAutoRefresh() {
  autoRefreshInterval.value = setInterval(async () => {
    await updatePerformanceMetrics();
  }, 10000); // Update every 10 seconds
}

function stopAutoRefresh() {
  if (autoRefreshInterval.value) {
    clearInterval(autoRefreshInterval.value);
    autoRefreshInterval.value = null;
  }
}

// Lifecycle
onMounted(async () => {
  await updatePerformanceMetrics();
  startAutoRefresh();
});

onUnmounted(() => {
  stopAutoRefresh();
});
</script>

<style scoped>
.service-status-component {
  max-width: 1200px;
  margin: 0 auto;
}

.service-card {
  transition: all 0.3s ease;
}

.service-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.service-healthy {
  border-left: 4px solid var(--q-positive);
}

.service-error {
  border-left: 4px solid var(--q-negative);
}

.service-initializing {
  border-left: 4px solid var(--q-warning);
}

.service-unknown {
  border-left: 4px solid var(--q-grey);
}
</style>
