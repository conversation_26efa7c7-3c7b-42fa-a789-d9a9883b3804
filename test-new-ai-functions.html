<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test New AI Functions</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .result {
            background: #f5f5f5;
            padding: 15px;
            margin: 10px 0;
            border-radius: 4px;
            white-space: pre-wrap;
        }
        .error {
            background: #ffebee;
            color: #c62828;
        }
        .success {
            background: #e8f5e8;
            color: #2e7d32;
        }
        button {
            background: #1976d2;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #1565c0;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        input, textarea {
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        textarea {
            height: 100px;
            resize: vertical;
        }
    </style>
</head>
<body>
    <h1>Test New AI Functions</h1>
    <p>Testing the migrated AI functions: <code>ai-generate-stream</code> and <code>ai-assessment</code></p>

    <!-- Test AI Generate Stream -->
    <div class="test-section">
        <h2>Test AI Generate Stream</h2>
        <div>
            <label>Prompt:</label>
            <textarea id="generatePrompt" placeholder="Enter your prompt here...">Tell me about Zimbabwe's innovation ecosystem and opportunities for startups.</textarea>
        </div>
        <div>
            <label>Section Type (optional):</label>
            <input type="text" id="sectionType" placeholder="e.g., innovation_analysis, general_assistance">
        </div>
        <button onclick="testAIGenerate()" id="generateBtn">Test AI Generate</button>
        <button onclick="testAIGenerateStream()" id="generateStreamBtn">Test AI Generate (Streaming)</button>
        <div id="generateResult" class="result" style="display: none;"></div>
    </div>

    <!-- Test AI Assessment -->
    <div class="test-section">
        <h2>Test AI Assessment</h2>
        <p>This requires authentication. Make sure you're logged in to test this function.</p>
        <button onclick="testAIAssessment()" id="assessmentBtn">Test AI Assessment</button>
        <div id="assessmentResult" class="result" style="display: none;"></div>
    </div>

    <!-- Configuration -->
    <div class="test-section">
        <h2>Configuration</h2>
        <div>
            <label>Supabase URL:</label>
            <input type="text" id="supabaseUrl" value="https://dpicnvisvxpmgjtbeicf.supabase.co">
        </div>
        <div>
            <label>Anon Key:</label>
            <input type="text" id="anonKey" value="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImRwaWNudmlzdnhwbWdqdGJlaWNmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM3OTE0MDgsImV4cCI6MjA1OTM2NzQwOH0.6x_KSp-mDrLNqZiSW3ZB4VFDPM5tQas0mc68Md-Uvrc" placeholder="Enter your Supabase anon key">
        </div>
        <div>
            <label>Access Token (for authenticated requests):</label>
            <input type="text" id="accessToken" placeholder="Enter your access token (optional)">
        </div>
    </div>

    <script>
        // Test AI Generate Stream (non-streaming)
        async function testAIGenerate() {
            const btn = document.getElementById('generateBtn');
            const result = document.getElementById('generateResult');
            const prompt = document.getElementById('generatePrompt').value;
            const sectionType = document.getElementById('sectionType').value;
            
            if (!prompt.trim()) {
                showResult('generateResult', 'Please enter a prompt', 'error');
                return;
            }

            btn.disabled = true;
            btn.textContent = 'Testing...';
            
            try {
                const response = await fetch(`${getSupabaseUrl()}/functions/v1/ai-generate-stream`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${getAuthToken()}`,
                    },
                    body: JSON.stringify({
                        prompt: prompt,
                        sectionType: sectionType || undefined,
                        options: {
                            stream: false,
                            maxTokens: 1000,
                            temperature: 0.7
                        }
                    })
                });

                console.log('Response status:', response.status);
                console.log('Response headers:', Object.fromEntries(response.headers.entries()));

                if (!response.ok) {
                    const errorText = await response.text();
                    throw new Error(`HTTP ${response.status}: ${errorText}`);
                }

                const data = await response.json();
                showResult('generateResult', JSON.stringify(data, null, 2), 'success');
                
            } catch (error) {
                console.error('Error:', error);
                showResult('generateResult', `Error: ${error.message}`, 'error');
            } finally {
                btn.disabled = false;
                btn.textContent = 'Test AI Generate';
            }
        }

        // Test AI Generate Stream (streaming)
        async function testAIGenerateStream() {
            const btn = document.getElementById('generateStreamBtn');
            const result = document.getElementById('generateResult');
            const prompt = document.getElementById('generatePrompt').value;
            const sectionType = document.getElementById('sectionType').value;
            
            if (!prompt.trim()) {
                showResult('generateResult', 'Please enter a prompt', 'error');
                return;
            }

            btn.disabled = true;
            btn.textContent = 'Streaming...';
            result.style.display = 'block';
            result.className = 'result success';
            result.textContent = 'Streaming response:\n\n';
            
            try {
                const response = await fetch(`${getSupabaseUrl()}/functions/v1/ai-generate-stream`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${getAuthToken()}`,
                    },
                    body: JSON.stringify({
                        prompt: prompt,
                        sectionType: sectionType || undefined,
                        options: {
                            stream: true,
                            maxTokens: 1000,
                            temperature: 0.7
                        }
                    })
                });

                if (!response.ok) {
                    const errorText = await response.text();
                    throw new Error(`HTTP ${response.status}: ${errorText}`);
                }

                const reader = response.body?.getReader();
                if (!reader) {
                    throw new Error('No response body');
                }

                const decoder = new TextDecoder();
                let buffer = '';

                while (true) {
                    const { done, value } = await reader.read();
                    if (done) break;

                    buffer += decoder.decode(value, { stream: true });
                    const lines = buffer.split('\n');
                    buffer = lines.pop() || '';

                    for (const line of lines) {
                        if (line.trim()) {
                            try {
                                const data = JSON.parse(line);
                                if (data.type === 'content') {
                                    result.textContent += data.content;
                                } else if (data.type === 'done') {
                                    result.textContent += '\n\n--- Streaming Complete ---';
                                } else if (data.type === 'error') {
                                    result.textContent += `\n\nError: ${data.error}`;
                                }
                            } catch (e) {
                                console.warn('Failed to parse line:', line);
                            }
                        }
                    }
                }
                
            } catch (error) {
                console.error('Error:', error);
                showResult('generateResult', `Error: ${error.message}`, 'error');
            } finally {
                btn.disabled = false;
                btn.textContent = 'Test AI Generate (Streaming)';
            }
        }

        // Test AI Assessment
        async function testAIAssessment() {
            const btn = document.getElementById('assessmentBtn');
            
            btn.disabled = true;
            btn.textContent = 'Testing...';
            
            try {
                // Sample project data for testing
                const projectData = {
                    id: 'test-project-123',
                    name: 'EcoTech Innovation',
                    description: 'A sustainable technology solution for waste management in Zimbabwe',
                    type: 'technology',
                    industry: 'environmental'
                };

                const moduleResponses = {
                    innovation_potential: 'High potential for market disruption',
                    market_analysis: 'Growing demand for sustainable solutions',
                    team_assessment: 'Experienced team with relevant expertise',
                    impact_evaluation: 'Significant environmental and social impact expected'
                };

                const response = await fetch(`${getSupabaseUrl()}/functions/v1/ai-assessment`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${getAuthToken()}`,
                    },
                    body: JSON.stringify({
                        projectData: projectData,
                        moduleResponses: moduleResponses,
                        analysisType: 'comprehensive'
                    })
                });

                console.log('Response status:', response.status);

                if (!response.ok) {
                    const errorText = await response.text();
                    throw new Error(`HTTP ${response.status}: ${errorText}`);
                }

                const data = await response.json();
                showResult('assessmentResult', JSON.stringify(data, null, 2), 'success');
                
            } catch (error) {
                console.error('Error:', error);
                showResult('assessmentResult', `Error: ${error.message}`, 'error');
            } finally {
                btn.disabled = false;
                btn.textContent = 'Test AI Assessment';
            }
        }

        // Helper functions
        function getSupabaseUrl() {
            return document.getElementById('supabaseUrl').value || 'https://dpicnvisvxpmgjtbeicf.supabase.co';
        }

        function getAuthToken() {
            const accessToken = document.getElementById('accessToken').value;
            const anonKey = document.getElementById('anonKey').value;
            return accessToken || anonKey || 'your-anon-key-here';
        }

        function showResult(elementId, content, type) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = `result ${type}`;
            element.textContent = content;
        }

        // Initialize with some default values
        window.onload = function() {
            // You can set default values here if needed
            console.log('Test page loaded. Ready to test AI functions.');
        };
    </script>
</body>
</html>
