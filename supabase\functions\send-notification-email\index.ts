// Supabase Edge Function for sending notification emails
import { serve } from 'https://deno.land/std@0.177.0/http/server.ts'
import { corsHeaders } from '../_shared/cors.ts'
import { generateConnectionRequestEmail } from './email-templates/connection-request.ts'
import { generateConnectionAcceptedEmail } from './email-templates/connection-accepted.ts'
import { generatePostLikeEmail } from './email-templates/post-like.ts'
import { generatePostCommentEmail } from './email-templates/post-comment.ts'
import { generateNewMessageEmail } from './email-templates/new-message.ts'
import { generateCommunityDigestEmail } from './email-templates/community-digest.ts'

// Environment variables
const RESEND_API_KEY = Deno.env.get('RESEND_KEY')
const DEFAULT_FROM_EMAIL = Deno.env.get('SENDGRID_FROM_EMAIL') || '<EMAIL>'
const DEFAULT_FROM_NAME = Deno.env.get('SENDGRID_FROM_NAME') || 'ZB Innovation Hub'

// Types for email data
interface EmailData {
  to: string
  subject: string
  html: string
  text?: string
  from?: {
    email: string
    name: string
  }
}

// Types for notification email request
interface NotificationEmailRequest {
  type: 'connection_request' | 'connection_accepted' | 'post_like' | 'post_comment' | 'new_message' | 'community_digest'
  data: {
    to: string
    firstName?: string
    lastName?: string
    // Connection-specific data
    requesterName?: string
    requesterProfileUrl?: string
    // Post-specific data
    postTitle?: string
    postUrl?: string
    likerName?: string
    commenterName?: string
    commentText?: string
    // Message-specific data
    senderName?: string
    messagePreview?: string
    conversationUrl?: string
    // Community digest data
    digestData?: {
      newPosts: number
      newMembers: number
      topPosts: Array<{ title: string; url: string; likes: number }>
      featuredContent: Array<{ title: string; url: string; type: string }>
    }
  }
}

// Function to send email via Resend
async function sendEmail(emailData: EmailData): Promise<Response> {
  if (!RESEND_API_KEY) {
    console.error('Resend API key is not configured')
    throw new Error('Resend API key is not configured')
  }

  console.log('Sending email with Resend:', {
    to: emailData.to,
    subject: emailData.subject,
    from: emailData.from || {
      email: DEFAULT_FROM_EMAIL,
      name: DEFAULT_FROM_NAME
    }
  })

  const url = 'https://api.resend.com/emails'

  // Prepare the payload for Resend
  const payload = {
    from: `${emailData.from?.name || DEFAULT_FROM_NAME} <${emailData.from?.email || DEFAULT_FROM_EMAIL}>`,
    to: [emailData.to],
    subject: emailData.subject,
    html: emailData.html,
    text: emailData.text || stripHtml(emailData.html)
  }

  console.log('Resend request payload:', JSON.stringify({
    ...payload,
    html: payload.html.length > 100 ? `${payload.html.substring(0, 100)}... (truncated)` : payload.html,
    text: payload.text.length > 100 ? `${payload.text.substring(0, 100)}... (truncated)` : payload.text
  }))

  const response = await fetch(url, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${RESEND_API_KEY}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(payload)
  })

  if (!response.ok) {
    let errorMessage = `Failed to send email: ${response.status} ${response.statusText}`
    try {
      const errorData = await response.json()
      errorMessage = `${errorMessage} - ${JSON.stringify(errorData)}`
    } catch (e) {
      // Ignore JSON parsing error
    }
    console.error('Resend API error:', errorMessage)
    throw new Error(errorMessage)
  }

  console.log('Email sent successfully')
  return response
}

// Function to strip HTML tags for plain text
function stripHtml(html: string): string {
  return html
    .replace(/<[^>]*>/g, '')
    .replace(/&nbsp;/g, ' ')
    .replace(/&amp;/g, '&')
    .replace(/&lt;/g, '<')
    .replace(/&gt;/g, '>')
    .replace(/&quot;/g, '"')
    .replace(/&#39;/g, "'")
    .replace(/\s+/g, ' ')
    .trim()
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const { type, data }: NotificationEmailRequest = await req.json()

    console.log('Notification email request:', { type, to: data.to })

    let emailTemplate: { html: string; subject: string }

    // Generate email template based on type
    switch (type) {
      case 'connection_request':
        emailTemplate = generateConnectionRequestEmail(
          data.to,
          data.requesterName || 'Someone',
          data.requesterProfileUrl || '#',
          data.firstName
        )
        break

      case 'connection_accepted':
        emailTemplate = generateConnectionAcceptedEmail(
          data.to,
          data.requesterName || 'Someone',
          data.requesterProfileUrl || '#',
          data.firstName
        )
        break

      case 'post_like':
        emailTemplate = generatePostLikeEmail(
          data.to,
          data.likerName || 'Someone',
          data.postTitle || 'your post',
          data.postUrl || '#',
          data.firstName
        )
        break

      case 'post_comment':
        emailTemplate = generatePostCommentEmail(
          data.to,
          data.commenterName || 'Someone',
          data.postTitle || 'your post',
          data.commentText || '',
          data.postUrl || '#',
          data.firstName
        )
        break

      case 'new_message':
        emailTemplate = generateNewMessageEmail(
          data.to,
          data.senderName || 'Someone',
          data.messagePreview || '',
          data.conversationUrl || '#',
          data.firstName
        )
        break

      case 'community_digest':
        emailTemplate = generateCommunityDigestEmail(
          data.to,
          data.digestData || { newPosts: 0, newMembers: 0, topPosts: [], featuredContent: [] },
          data.firstName
        )
        break

      default:
        throw new Error(`Unknown notification email type: ${type}`)
    }

    // Send the email
    await sendEmail({
      to: data.to,
      subject: emailTemplate.subject,
      html: emailTemplate.html
    })

    console.log(`Notification email sent successfully to ${data.to}`)

    return new Response(
      JSON.stringify({ success: true, message: 'Notification email sent successfully' }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200
      }
    )

  } catch (error: any) {
    console.error('Error sending notification email:', error)
    return new Response(
      JSON.stringify({ success: false, error: error.message }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500
      }
    )
  }
})
