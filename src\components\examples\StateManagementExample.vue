<template>
  <q-card class="state-management-example">
    <q-card-section>
      <div class="text-h6">
        <q-icon name="settings" class="q-mr-sm" />
        State Management Example
      </div>
      <div class="text-caption text-grey-6">
        Example of how to use services through global state management
      </div>
    </q-card-section>

    <q-card-section>
      <!-- Service Status -->
      <div class="q-mb-md">
        <div class="text-subtitle2 q-mb-sm">Service Status</div>
        <div class="row q-gutter-sm">
          <q-chip
            v-for="(status, serviceName) in globalServices.serviceStatus"
            :key="serviceName"
            :color="status.initialized ? 'positive' : status.initializing ? 'warning' : 'negative'"
            text-color="white"
            :icon="status.initialized ? 'check' : status.initializing ? 'hourglass_empty' : 'error'"
          >
            {{ serviceName }}
          </q-chip>
        </div>
      </div>

      <!-- AI Service Example -->
      <div class="q-mb-md">
        <div class="text-subtitle2 q-mb-sm">AI Service (via Global Store)</div>
        <q-btn
          @click="testAIService"
          color="primary"
          label="Test AI Chat"
          icon="chat"
          :loading="aiLoading"
          :disable="!globalServices.allInitialized"
        />
        <div v-if="aiResponse" class="q-mt-sm q-pa-sm bg-grey-1 rounded-borders">
          {{ aiResponse }}
        </div>
      </div>

      <!-- Profile Service Example -->
      <div class="q-mb-md">
        <div class="text-subtitle2 q-mb-sm">Profile Service (via Global Store)</div>
        <q-btn
          @click="testProfileService"
          color="secondary"
          label="Load Profile"
          icon="person"
          :loading="profileLoading"
          :disable="!globalServices.allInitialized"
        />
        <div v-if="profileData" class="q-mt-sm q-pa-sm bg-grey-1 rounded-borders">
          Profile loaded: {{ profileData.full_name || 'No name' }}
        </div>
      </div>

      <!-- Cache Service Example -->
      <div class="q-mb-md">
        <div class="text-subtitle2 q-mb-sm">Cache Service (via Global Store)</div>
        <div class="row q-gutter-sm">
          <q-btn
            @click="testCacheSet"
            color="accent"
            label="Set Cache"
            icon="save"
            size="sm"
          />
          <q-btn
            @click="testCacheGet"
            color="accent"
            label="Get Cache"
            icon="cached"
            size="sm"
          />
        </div>
        <div v-if="cacheResult" class="q-mt-sm q-pa-sm bg-grey-1 rounded-borders">
          Cache result: {{ cacheResult }}
        </div>
      </div>

      <!-- Connection Service Example -->
      <div class="q-mb-md">
        <div class="text-subtitle2 q-mb-sm">Connection Service (via Global Store)</div>
        <q-btn
          @click="testConnectionService"
          color="info"
          label="Test Connections"
          icon="people"
          :loading="connectionLoading"
          :disable="!globalServices.allInitialized"
        />
        <div v-if="connectionResult" class="q-mt-sm q-pa-sm bg-grey-1 rounded-borders">
          {{ connectionResult }}
        </div>
      </div>
    </q-card-section>

    <q-card-section>
      <div class="text-caption text-grey-6">
        <strong>Best Practice:</strong> Always access services through the global store instead of importing them directly.
        This ensures proper state management, easier testing, and consistent service initialization.
      </div>
    </q-card-section>
  </q-card>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useGlobalServicesStore } from '@/stores/globalServices';
import { useAuthStore } from '@/stores/auth';

// Use global services store instead of direct imports
const globalServices = useGlobalServicesStore();
const authStore = useAuthStore();

// Reactive state
const aiLoading = ref(false);
const aiResponse = ref('');
const profileLoading = ref(false);
const profileData = ref<any>(null);
const cacheResult = ref('');
const connectionLoading = ref(false);
const connectionResult = ref('');

// AI Service Example
async function testAIService() {
  aiLoading.value = true;
  aiResponse.value = '';
  
  try {
    // Access AI service through global store
    const response = await globalServices.aiEnhancedService.sendEnhancedChatMessage({
      message: "Hello, this is a test message from the state management example.",
      conversation_history: [],
      user_context: {
        is_authenticated: authStore.isAuthenticated,
        current_page: 'state-management-example'
      }
    });
    
    aiResponse.value = response.response || 'No response received';
  } catch (error: any) {
    aiResponse.value = `Error: ${error.message}`;
  } finally {
    aiLoading.value = false;
  }
}

// Profile Service Example
async function testProfileService() {
  profileLoading.value = true;
  profileData.value = null;
  
  try {
    if (!authStore.currentUser?.id) {
      profileData.value = { error: 'No authenticated user' };
      return;
    }
    
    // Access profile service through global store
    const profile = await globalServices.profileManager.getProfile(authStore.currentUser.id);
    profileData.value = profile || { error: 'No profile found' };
  } catch (error: any) {
    profileData.value = { error: error.message };
  } finally {
    profileLoading.value = false;
  }
}

// Cache Service Example
function testCacheSet() {
  // Access cache service through global store
  globalServices.cacheService.set('example-key', {
    message: 'Hello from cache!',
    timestamp: new Date().toISOString()
  });
  cacheResult.value = 'Data cached successfully';
}

function testCacheGet() {
  // Access cache service through global store
  const cached = globalServices.cacheService.get('example-key');
  cacheResult.value = cached ? JSON.stringify(cached) : 'No cached data found';
}

// Connection Service Example
async function testConnectionService() {
  connectionLoading.value = true;
  connectionResult.value = '';
  
  try {
    if (!authStore.currentUser?.id) {
      connectionResult.value = 'No authenticated user';
      return;
    }
    
    // Access connection service through global store
    const connections = await globalServices.connectionService.getUserConnections(authStore.currentUser.id);
    connectionResult.value = `Found ${connections?.length || 0} connections`;
  } catch (error: any) {
    connectionResult.value = `Error: ${error.message}`;
  } finally {
    connectionLoading.value = false;
  }
}
</script>

<style scoped>
.state-management-example {
  max-width: 800px;
  margin: 0 auto;
}
</style>
