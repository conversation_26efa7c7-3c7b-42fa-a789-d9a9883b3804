/**
 * State Management Integration Test Suite
 * 
 * Comprehensive tests to validate that all services work correctly
 * through the new state management architecture.
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { setActivePinia, createPinia } from 'pinia';
import { useGlobalServicesStore } from '../../stores/globalServices';
import { useServiceInitialization } from '../../composables/useServiceInitialization';

describe('State Management Integration', () => {
  let pinia: ReturnType<typeof createPinia>;
  let globalServices: ReturnType<typeof useGlobalServicesStore>;

  beforeEach(() => {
    pinia = createPinia();
    setActivePinia(pinia);
    globalServices = useGlobalServicesStore();
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('Global Services Store', () => {
    it('should initialize all services successfully', async () => {
      const result = await globalServices.initializeAllServices();
      
      expect(result).toBe(true);
      expect(globalServices.allInitialized).toBe(true);
      expect(globalServices.hasErrors).toBe(false);
    });

    it('should provide access to all required services', () => {
      expect(globalServices.cacheService).toBeDefined();
      expect(globalServices.realtimeService).toBeDefined();
      expect(globalServices.activityService).toBeDefined();
      expect(globalServices.connectionService).toBeDefined();
      expect(globalServices.profileService).toBeDefined();
      expect(globalServices.profileManager).toBeDefined();
      expect(globalServices.profileCompletionService).toBeDefined();
      expect(globalServices.matchmakingService).toBeDefined();
      expect(globalServices.aiChatTriggerService).toBeDefined();
      expect(globalServices.aiEnhancedService).toBeDefined();
    });

    it('should validate service dependencies correctly', async () => {
      await globalServices.initializeAllServices();
      
      const validation = globalServices.validateServiceDependencies();
      expect(validation.valid).toBe(true);
      expect(validation.issues).toHaveLength(0);
    });

    it('should provide accurate service health monitoring', async () => {
      await globalServices.initializeAllServices();
      
      const health = globalServices.serviceHealth;
      expect(health.status).toBe('HEALTHY');
      expect(health.healthPercentage).toBe(100);
      expect(health.failedServices).toHaveLength(0);
    });

    it('should handle service recovery gracefully', async () => {
      await globalServices.initializeAllServices();
      
      // Simulate a service failure
      globalServices.serviceStatus.cache.error = 'Test error';
      globalServices.serviceStatus.cache.initialized = false;
      
      // Attempt recovery
      await globalServices.recoverFailedServices();
      
      // Verify recovery
      expect(globalServices.serviceStatus.cache.error).toBeNull();
      expect(globalServices.serviceStatus.cache.initialized).toBe(true);
    });
  });

  describe('Service Initialization Composable', () => {
    it('should initialize services with retry logic', async () => {
      const { initializeServices, isReady, hasErrors } = useServiceInitialization({
        autoInitialize: false,
        maxRetries: 2
      });
      
      const result = await initializeServices();
      
      expect(result).toBe(true);
      expect(isReady.value).toBe(true);
      expect(hasErrors.value).toBe(false);
    });

    it('should provide accurate service status', async () => {
      const { isReady, isInitializing, serviceHealth } = useServiceInitialization({
        autoInitialize: false
      });
      
      expect(isReady.value).toBe(false);
      expect(isInitializing.value).toBe(false);
      
      await globalServices.initializeAllServices();
      
      expect(isReady.value).toBe(true);
      expect(serviceHealth.value.status).toBe('HEALTHY');
    });
  });

  describe('Service Coordination', () => {
    it('should initialize services in correct dependency order', async () => {
      const initOrder: string[] = [];
      
      // Mock service initializers to track order
      const originalInit = globalServices.initializeAllServices;
      globalServices.initializeAllServices = vi.fn().mockImplementation(async () => {
        initOrder.push('cache', 'realtime', 'activity', 'profile', 'connection', 'matchmaking', 'ai');
        return originalInit.call(globalServices);
      });
      
      await globalServices.initializeAllServices();
      
      expect(initOrder).toEqual(['cache', 'realtime', 'activity', 'profile', 'connection', 'matchmaking', 'ai']);
    });

    it('should handle partial service failures gracefully', async () => {
      // Simulate a service initialization failure
      const originalConnectionInit = globalServices.initializeConnectionService;
      globalServices.initializeConnectionService = vi.fn().mockRejectedValue(new Error('Connection service failed'));
      
      try {
        await globalServices.initializeAllServices();
      } catch (error) {
        // Expected to fail
      }
      
      // Verify that other services still initialized
      expect(globalServices.serviceStatus.cache.initialized).toBe(true);
      expect(globalServices.serviceStatus.realtime.initialized).toBe(true);
      expect(globalServices.serviceStatus.connection.error).toBeTruthy();
      
      // Restore original function
      globalServices.initializeConnectionService = originalConnectionInit;
    });
  });

  describe('Service Integration Points', () => {
    beforeEach(async () => {
      await globalServices.initializeAllServices();
    });

    it('should allow AI services to access other services', () => {
      const aiService = globalServices.aiChatTriggerService;
      expect(aiService).toBeDefined();
      expect(typeof aiService.triggerChat).toBe('function');
    });

    it('should allow profile services to access cache', () => {
      const profileManager = globalServices.profileManager;
      const cacheService = globalServices.cacheService;
      
      expect(profileManager).toBeDefined();
      expect(cacheService).toBeDefined();
      expect(typeof profileManager.invalidateProfile).toBe('function');
    });

    it('should allow connection service to access activity service', () => {
      const connectionService = globalServices.connectionService;
      const activityService = globalServices.activityService;
      
      expect(connectionService).toBeDefined();
      expect(activityService).toBeDefined();
      expect(typeof connectionService.connectWithUser).toBe('function');
    });

    it('should provide consistent service instances across calls', () => {
      const cache1 = globalServices.cacheService;
      const cache2 = globalServices.cacheService;
      
      expect(cache1).toBe(cache2);
      
      const profile1 = globalServices.profileService;
      const profile2 = globalServices.profileService;
      
      expect(profile1).toBe(profile2);
    });
  });

  describe('Error Handling and Recovery', () => {
    it('should detect and report service dependency violations', async () => {
      // Manually set up an invalid state
      globalServices.serviceStatus.connection.initialized = true;
      globalServices.serviceStatus.activity.initialized = false;
      
      const validation = globalServices.validateServiceDependencies();
      
      expect(validation.valid).toBe(false);
      expect(validation.issues).toContain('Connection service initialized without activity service dependency');
    });

    it('should recover from service failures', async () => {
      await globalServices.initializeAllServices();
      
      // Simulate multiple service failures
      globalServices.serviceStatus.cache.error = 'Cache error';
      globalServices.serviceStatus.cache.initialized = false;
      globalServices.serviceStatus.realtime.error = 'Realtime error';
      globalServices.serviceStatus.realtime.initialized = false;
      
      // Attempt recovery
      await globalServices.recoverFailedServices();
      
      // Verify all services are recovered
      expect(globalServices.serviceStatus.cache.error).toBeNull();
      expect(globalServices.serviceStatus.cache.initialized).toBe(true);
      expect(globalServices.serviceStatus.realtime.error).toBeNull();
      expect(globalServices.serviceStatus.realtime.initialized).toBe(true);
    });

    it('should shutdown services gracefully', async () => {
      await globalServices.initializeAllServices();
      
      expect(globalServices.allInitialized).toBe(true);
      
      await globalServices.shutdownAllServices();
      
      // Verify all services are shut down
      Object.values(globalServices.serviceStatus).forEach(status => {
        expect(status.initialized).toBe(false);
        expect(status.initializing).toBe(false);
        expect(status.error).toBeNull();
      });
    });
  });

  describe('Component Integration', () => {
    it('should allow components to access services through global store', async () => {
      await globalServices.initializeAllServices();

      // Simulate component accessing services
      const componentServices = {
        cache: globalServices.cacheService,
        activity: globalServices.activityService,
        profile: globalServices.profileService,
        ai: globalServices.aiChatTriggerService
      };

      expect(componentServices.cache).toBeDefined();
      expect(componentServices.activity).toBeDefined();
      expect(componentServices.profile).toBeDefined();
      expect(componentServices.ai).toBeDefined();

      // Verify services have expected methods
      expect(typeof componentServices.cache.get).toBe('function');
      expect(typeof componentServices.cache.set).toBe('function');
      expect(typeof componentServices.activity.trackActivity).toBe('function');
      expect(typeof componentServices.profile.formatProfileType).toBe('function');
      expect(typeof componentServices.ai.triggerChat).toBe('function');
    });

    it('should maintain service state across component lifecycle', async () => {
      await globalServices.initializeAllServices();

      // Simulate component mounting and using cache
      const cache = globalServices.cacheService;
      cache.set('test-key', 'test-value');

      // Simulate component unmounting and remounting
      const newCache = globalServices.cacheService;
      const cachedValue = newCache.get('test-key');

      expect(cachedValue).toBe('test-value');
      expect(cache).toBe(newCache); // Same instance
    });
  });

  describe('Performance and Memory', () => {
    it('should not create duplicate service instances', async () => {
      await globalServices.initializeAllServices();

      const instances1 = {
        cache: globalServices.cacheService,
        realtime: globalServices.realtimeService,
        activity: globalServices.activityService,
        profile: globalServices.profileService
      };

      const instances2 = {
        cache: globalServices.cacheService,
        realtime: globalServices.realtimeService,
        activity: globalServices.activityService,
        profile: globalServices.profileService
      };

      expect(instances1.cache).toBe(instances2.cache);
      expect(instances1.realtime).toBe(instances2.realtime);
      expect(instances1.activity).toBe(instances2.activity);
      expect(instances1.profile).toBe(instances2.profile);
    });

    it('should initialize services efficiently', async () => {
      const startTime = Date.now();

      await globalServices.initializeAllServices();

      const endTime = Date.now();
      const initializationTime = endTime - startTime;

      // Initialization should complete within reasonable time (5 seconds)
      expect(initializationTime).toBeLessThan(5000);
      expect(globalServices.allInitialized).toBe(true);
    });
  });
});
