import { test, expect } from '@playwright/test';

// Test configuration
const BASE_URL = 'http://localhost:5175';
const TEST_USER_EMAIL = '<EMAIL>';
const TEST_USER_PASSWORD = 'Superb@23';

test.describe('AI Chat Assistant', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the home page
    await page.goto(BASE_URL);
    
    // Wait for the page to load
    await page.waitForLoadState('networkidle');
  });

  test.describe('Unauthenticated User Flow', () => {
    test('should display AI chat button on landing page', async ({ page }) => {
      // Check if AI chat button is visible
      const aiChatButton = page.getByTestId('ai-chat-toggle');
      await expect(aiChatButton).toBeVisible();
    });

    test('should open AI chat window when button is clicked', async ({ page }) => {
      // Click AI chat button
      await page.getByTestId('ai-chat-toggle').click();
      
      // Check if chat window is visible
      const chatWindow = page.locator('[data-testid="ai-chat-window"]');
      await expect(chatWindow).toBeVisible();
      
      // Check if welcome message is displayed
      const welcomeMessage = page.locator('text=Welcome to ZbInnovation!');
      await expect(welcomeMessage).toBeVisible();
      
      // Check if suggestions are displayed
      const suggestions = page.locator('[data-testid="ai-chat-suggestions"]');
      await expect(suggestions).toBeVisible();
    });

    test('should send message and receive AI response', async ({ page }) => {
      // Open AI chat
      await page.getByTestId('ai-chat-toggle').click();
      
      // Type a message
      const testMessage = 'Hello, how can you help me?';
      await page.getByTestId('ai-chat-input').fill(testMessage);
      
      // Send the message
      await page.getByTestId('ai-chat-send').click();
      
      // Check if user message appears
      const userMessage = page.locator(`text=${testMessage}`);
      await expect(userMessage).toBeVisible();
      
      // Wait for AI response (with timeout)
      await page.waitForSelector('[data-testid="ai-message"]', { timeout: 30000 });
      
      // Check if AI response appears
      const aiResponse = page.locator('[data-testid="ai-message"]').last();
      await expect(aiResponse).toBeVisible();
      
      // Check if response contains expected content for unauthenticated users
      const responseText = await aiResponse.textContent();
      expect(responseText).toContain('ZbInnovation');
    });

    test('should display appropriate suggestions for unauthenticated users', async ({ page }) => {
      // Open AI chat
      await page.getByTestId('ai-chat-toggle').click();
      
      // Check for unauthenticated user suggestions
      const suggestions = [
        'How do I sign up for the platform?',
        'What features are available?',
        'Tell me about the innovation community',
        'How can I connect with investors?'
      ];
      
      for (const suggestion of suggestions) {
        const suggestionElement = page.locator(`text=${suggestion}`);
        await expect(suggestionElement).toBeVisible();
      }
    });

    test('should show signup action button for unauthenticated users', async ({ page }) => {
      // Open AI chat
      await page.getByTestId('ai-chat-toggle').click();
      
      // Send a message to trigger AI response
      await page.getByTestId('ai-chat-input').fill('How do I get started?');
      await page.getByTestId('ai-chat-send').click();
      
      // Wait for response with action buttons
      await page.waitForSelector('[data-testid="ai-action-button"]', { timeout: 30000 });
      
      // Check for signup/signin action buttons
      const actionButtons = page.locator('[data-testid="ai-action-button"]');
      const buttonCount = await actionButtons.count();
      expect(buttonCount).toBeGreaterThan(0);
      
      // Check if at least one button is for signup/signin
      const buttonTexts = await actionButtons.allTextContents();
      const hasAuthButton = buttonTexts.some(text => 
        text.includes('Sign Up') || text.includes('Sign In') || text.includes('Get Started')
      );
      expect(hasAuthButton).toBe(true);
    });
  });

  test.describe('Authenticated User Flow', () => {
    test.beforeEach(async ({ page }) => {
      // Login before each authenticated test
      await page.getByText('Sign In').click();
      
      // Wait for login form
      await page.waitForSelector('input[type="email"]');
      
      // Fill login form
      await page.fill('input[type="email"]', TEST_USER_EMAIL);
      await page.fill('input[type="password"]', TEST_USER_PASSWORD);
      
      // Submit login
      await page.getByRole('button', { name: /sign in/i }).click();
      
      // Wait for successful login (redirect to dashboard)
      await page.waitForURL('**/dashboard**', { timeout: 10000 });
    });

    test('should display different suggestions for authenticated users', async ({ page }) => {
      // Navigate back to home page to test AI chat
      await page.goto(BASE_URL);
      
      // Open AI chat
      await page.getByTestId('ai-chat-toggle').click();
      
      // Send a message to get personalized response
      await page.getByTestId('ai-chat-input').fill('What can I do on this platform?');
      await page.getByTestId('ai-chat-send').click();
      
      // Wait for AI response
      await page.waitForSelector('[data-testid="ai-message"]', { timeout: 30000 });
      
      // Check for authenticated user specific suggestions
      const authenticatedSuggestions = [
        'How can I improve my profile?',
        'Show me networking opportunities',
        'Help me find relevant events',
        'What funding options are available?'
      ];
      
      // At least some authenticated suggestions should be present
      let foundAuthSuggestions = 0;
      for (const suggestion of authenticatedSuggestions) {
        const suggestionElement = page.locator(`text=${suggestion}`);
        if (await suggestionElement.isVisible()) {
          foundAuthSuggestions++;
        }
      }
      expect(foundAuthSuggestions).toBeGreaterThan(0);
    });

    test('should show authenticated action buttons', async ({ page }) => {
      // Navigate back to home page
      await page.goto(BASE_URL);
      
      // Open AI chat
      await page.getByTestId('ai-chat-toggle').click();
      
      // Send a message to trigger AI response
      await page.getByTestId('ai-chat-input').fill('Help me complete my profile');
      await page.getByTestId('ai-chat-send').click();
      
      // Wait for response with action buttons
      await page.waitForSelector('[data-testid="ai-action-button"]', { timeout: 30000 });
      
      // Check for authenticated user action buttons
      const actionButtons = page.locator('[data-testid="ai-action-button"]');
      const buttonTexts = await actionButtons.allTextContents();
      
      // Should have buttons like "Complete Profile", "View Dashboard", etc.
      const hasAuthenticatedButtons = buttonTexts.some(text => 
        text.includes('Complete Profile') || 
        text.includes('View Dashboard') || 
        text.includes('Create Post') ||
        text.includes('Explore Community')
      );
      expect(hasAuthenticatedButtons).toBe(true);
    });
  });

  test.describe('Streaming Functionality', () => {
    test('should display streaming response in real-time', async ({ page }) => {
      // Open AI chat
      await page.getByTestId('ai-chat-toggle').click();
      
      // Send a message
      await page.getByTestId('ai-chat-input').fill('Tell me about the innovation ecosystem');
      await page.getByTestId('ai-chat-send').click();
      
      // Check if loading indicator appears
      const loadingIndicator = page.locator('[data-testid="ai-loading"]');
      await expect(loadingIndicator).toBeVisible();
      
      // Wait for streaming to start
      await page.waitForSelector('[data-testid="ai-message"]', { timeout: 30000 });
      
      // Check if response appears gradually (streaming)
      const aiMessage = page.locator('[data-testid="ai-message"]').last();
      await expect(aiMessage).toBeVisible();
      
      // Wait for streaming to complete (loading indicator should disappear)
      await expect(loadingIndicator).not.toBeVisible({ timeout: 30000 });
      
      // Check if final response has substantial content
      const responseText = await aiMessage.textContent();
      expect(responseText?.length).toBeGreaterThan(50);
    });
  });

  test.describe('Action Buttons', () => {
    test('should navigate correctly when action buttons are clicked', async ({ page }) => {
      // Open AI chat
      await page.getByTestId('ai-chat-toggle').click();
      
      // Send a message to get action buttons
      await page.getByTestId('ai-chat-input').fill('Show me the community');
      await page.getByTestId('ai-chat-send').click();
      
      // Wait for action buttons
      await page.waitForSelector('[data-testid="ai-action-button"]', { timeout: 30000 });
      
      // Find and click a navigation button
      const communityButton = page.locator('[data-testid="ai-action-button"]', { hasText: 'Community' }).first();
      if (await communityButton.isVisible()) {
        await communityButton.click();
        
        // Check if navigation occurred
        await page.waitForURL('**/virtual-community**', { timeout: 10000 });
        expect(page.url()).toContain('virtual-community');
      }
    });
  });

  test.describe('Error Scenarios', () => {
    test('should handle network errors gracefully', async ({ page }) => {
      // Intercept and block the AI chat API call
      await page.route('**/functions/v1/ai-enhanced-chat', route => {
        route.abort('failed');
      });
      
      // Open AI chat
      await page.getByTestId('ai-chat-toggle').click();
      
      // Send a message
      await page.getByTestId('ai-chat-input').fill('This should fail');
      await page.getByTestId('ai-chat-send').click();
      
      // Check if error is handled gracefully
      // The UI should show some error indication or fallback
      await page.waitForTimeout(5000);
      
      // Check if loading state is cleared
      const loadingIndicator = page.locator('[data-testid="ai-loading"]');
      await expect(loadingIndicator).not.toBeVisible();
    });
  });

  test.describe('Performance', () => {
    test('should respond within reasonable time limits', async ({ page }) => {
      // Open AI chat
      await page.getByTestId('ai-chat-toggle').click();
      
      // Measure response time
      const startTime = Date.now();
      
      // Send a message
      await page.getByTestId('ai-chat-input').fill('Quick test message');
      await page.getByTestId('ai-chat-send').click();
      
      // Wait for response
      await page.waitForSelector('[data-testid="ai-message"]', { timeout: 30000 });
      
      const endTime = Date.now();
      const responseTime = endTime - startTime;
      
      // Response should be within 30 seconds
      expect(responseTime).toBeLessThan(30000);
      
      console.log(`AI response time: ${responseTime}ms`);
    });
  });
});
