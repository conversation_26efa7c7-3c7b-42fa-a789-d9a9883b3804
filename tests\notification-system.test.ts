/**
 * Notification System Integration Tests
 * 
 * These tests validate the notification system functionality
 * including database operations, email queueing, and user preferences.
 */

import { describe, it, expect, beforeEach, afterEach } from 'vitest'
import { createClient } from '@supabase/supabase-js'
import { integratedNotificationService } from '../src/services/integratedNotificationService'

// Test configuration
const supabaseUrl = process.env.VITE_SUPABASE_URL || 'http://localhost:54321'
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY || 'test-key'
const supabase = createClient(supabaseUrl, supabaseKey)

// Test user IDs (use actual UUIDs in real tests)
const testUserId1 = '00000000-0000-0000-0000-000000000001'
const testUserId2 = '00000000-0000-0000-0000-000000000002'

describe('Notification System', () => {
  beforeEach(async () => {
    // Clean up test data before each test
    await cleanupTestData()
  })

  afterEach(async () => {
    // Clean up test data after each test
    await cleanupTestData()
  })

  describe('Database Functions', () => {
    it('should create notification with email queue', async () => {
      // Test the create_notification_with_email function
      const { data, error } = await supabase.rpc('create_notification_with_email', {
        p_user_id: testUserId1,
        p_type: 'connection_request',
        p_title: 'Test Notification',
        p_message: 'This is a test notification',
        p_data: { test: true }
      })

      expect(error).toBeNull()
      expect(data).toBeTruthy()

      // Verify notification was created
      const { data: notifications } = await supabase
        .from('notifications')
        .select('*')
        .eq('user_id', testUserId1)
        .eq('title', 'Test Notification')

      expect(notifications).toHaveLength(1)
      expect(notifications[0].type).toBe('connection_request')
    })

    it('should respect user notification preferences', async () => {
      // Set user preferences to disable email
      await supabase.rpc('update_notification_preferences', {
        p_email_enabled: false,
        p_connection_requests: false
      })

      // Check if email should be sent
      const { data: shouldSend } = await supabase.rpc('should_send_email_notification', {
        p_user_id: testUserId1,
        p_notification_type: 'connection_request'
      })

      expect(shouldSend).toBe(false)
    })

    it('should get user notification preferences', async () => {
      const { data: preferences, error } = await supabase.rpc('get_notification_preferences')

      expect(error).toBeNull()
      expect(preferences).toBeTruthy()
      expect(preferences).toHaveProperty('email_enabled')
      expect(preferences).toHaveProperty('connection_requests')
    })

    it('should mark notifications as read', async () => {
      // Create a test notification
      const { data: notificationId } = await supabase.rpc('create_notification', {
        p_user_id: testUserId1,
        p_type: 'connection_request',
        p_title: 'Test Notification',
        p_message: 'Test message',
        p_data: {}
      })

      // Mark as read
      const { error } = await supabase.rpc('mark_notification_read', {
        p_notification_id: notificationId
      })

      expect(error).toBeNull()

      // Verify it's marked as read
      const { data: notification } = await supabase
        .from('notifications')
        .select('read')
        .eq('id', notificationId)
        .single()

      expect(notification.read).toBe(true)
    })
  })

  describe('Integrated Notification Service', () => {
    it('should create connection request notification', async () => {
      const result = await integratedNotificationService.createConnectionRequestNotification(
        testUserId1,
        testUserId2,
        'test-connection-id'
      )

      expect(result).toBe(true)

      // Verify notification was created
      const notifications = await integratedNotificationService.getUserNotifications(10, 0)
      expect(notifications.length).toBeGreaterThan(0)
      
      const connectionNotification = notifications.find(n => n.type === 'connection_request')
      expect(connectionNotification).toBeTruthy()
    })

    it('should create connection accepted notification', async () => {
      const result = await integratedNotificationService.createConnectionAcceptedNotification(
        testUserId1,
        testUserId2,
        'test-connection-id'
      )

      expect(result).toBe(true)

      // Verify notification was created
      const notifications = await integratedNotificationService.getUserNotifications(10, 0)
      const acceptedNotification = notifications.find(n => n.type === 'connection_accepted')
      expect(acceptedNotification).toBeTruthy()
    })

    it('should create post like notification', async () => {
      const result = await integratedNotificationService.createPostLikeNotification(
        testUserId1,
        testUserId2,
        'test-post-id',
        'Test Post Title'
      )

      expect(result).toBe(true)

      // Verify notification was created
      const notifications = await integratedNotificationService.getUserNotifications(10, 0)
      const likeNotification = notifications.find(n => n.type === 'post_liked')
      expect(likeNotification).toBeTruthy()
      expect(likeNotification.data.post_title).toBe('Test Post Title')
    })

    it('should get unread notification count', async () => {
      // Create some test notifications
      await integratedNotificationService.createNotification({
        userId: testUserId1,
        type: 'connection_request',
        title: 'Test 1',
        message: 'Test message 1'
      })

      await integratedNotificationService.createNotification({
        userId: testUserId1,
        type: 'post_liked',
        title: 'Test 2',
        message: 'Test message 2'
      })

      const count = await integratedNotificationService.getUnreadCount()
      expect(count).toBeGreaterThanOrEqual(2)
    })

    it('should mark all notifications as read', async () => {
      // Create test notifications
      await integratedNotificationService.createNotification({
        userId: testUserId1,
        type: 'connection_request',
        title: 'Test 1',
        message: 'Test message 1'
      })

      // Mark all as read
      const markedCount = await integratedNotificationService.markAllAsRead()
      expect(markedCount).toBeGreaterThan(0)

      // Verify unread count is 0
      const unreadCount = await integratedNotificationService.getUnreadCount()
      expect(unreadCount).toBe(0)
    })
  })

  describe('Email Queue', () => {
    it('should queue email when notification is created', async () => {
      // Create notification that should trigger email
      await integratedNotificationService.createNotification({
        userId: testUserId1,
        type: 'connection_request',
        title: 'Test Connection Request',
        message: 'Someone wants to connect'
      })

      // Check if email was queued
      const { data: queuedEmails } = await supabase
        .from('email_queue')
        .select('*')
        .eq('user_id', testUserId1)
        .eq('email_type', 'connection_request')

      expect(queuedEmails.length).toBeGreaterThan(0)
      expect(queuedEmails[0].status).toBe('pending')
    })

    it('should not queue email when user has disabled email notifications', async () => {
      // Disable email notifications
      await supabase.rpc('update_notification_preferences', {
        p_email_enabled: false
      })

      // Create notification
      await integratedNotificationService.createNotification({
        userId: testUserId1,
        type: 'connection_request',
        title: 'Test Connection Request',
        message: 'Someone wants to connect'
      })

      // Check that no email was queued
      const { data: queuedEmails } = await supabase
        .from('email_queue')
        .select('*')
        .eq('user_id', testUserId1)
        .eq('email_type', 'connection_request')

      expect(queuedEmails).toHaveLength(0)
    })
  })

  describe('Error Handling', () => {
    it('should handle invalid user ID gracefully', async () => {
      const result = await integratedNotificationService.createNotification({
        userId: 'invalid-uuid',
        type: 'connection_request',
        title: 'Test',
        message: 'Test message'
      })

      expect(result).toBe(false)
    })

    it('should handle missing notification data gracefully', async () => {
      const result = await integratedNotificationService.createNotification({
        userId: testUserId1,
        type: 'connection_request',
        title: '',
        message: ''
      })

      // Should still create notification even with empty title/message
      expect(result).toBe(true)
    })
  })
})

/**
 * Helper function to clean up test data
 */
async function cleanupTestData() {
  try {
    // Clean up notifications
    await supabase
      .from('notifications')
      .delete()
      .in('user_id', [testUserId1, testUserId2])

    // Clean up email queue
    await supabase
      .from('email_queue')
      .delete()
      .in('user_id', [testUserId1, testUserId2])

    // Reset notification preferences
    await supabase
      .from('notification_preferences')
      .delete()
      .in('user_id', [testUserId1, testUserId2])

  } catch (error) {
    console.warn('Cleanup error (expected in some cases):', error)
  }
}
