/**
 * Main Application Integration Validation
 * 
 * Comprehensive validation script to ensure the main app integration
 * is working correctly in both development and production environments.
 */

export interface ValidationTest {
  name: string;
  description: string;
  test: () => Promise<boolean> | boolean;
  critical: boolean;
}

export interface ValidationResult {
  testName: string;
  passed: boolean;
  error?: string;
  duration: number;
  critical: boolean;
}

export interface IntegrationValidationReport {
  overallStatus: 'PASSED' | 'FAILED' | 'PARTIAL';
  environment: string;
  timestamp: Date;
  totalTests: number;
  passedTests: number;
  failedTests: number;
  criticalFailures: number;
  results: ValidationResult[];
  summary: string;
  recommendations: string[];
}

export class MainAppIntegrationValidator {
  private tests: ValidationTest[] = [
    {
      name: 'Pinia Store Initialization',
      description: 'Verify Pinia store is properly initialized',
      critical: true,
      test: () => {
        try {
          const services = (window as any).__GLOBAL_SERVICES__;
          return !!services && typeof services === 'object';
        } catch {
          return false;
        }
      }
    },
    {
      name: 'Service Coordination Plugin',
      description: 'Verify service coordination plugin is active',
      critical: true,
      test: () => {
        try {
          const coordinator = (window as any).__SERVICE_COORDINATOR__;
          return !!coordinator && typeof coordinator.getServiceHealth === 'function';
        } catch {
          return false;
        }
      }
    },
    {
      name: 'Global Error Handler',
      description: 'Verify global error handler is installed',
      critical: true,
      test: () => {
        try {
          const errorHandler = (window as any).__ERROR_HANDLER__;
          return !!errorHandler && typeof errorHandler.getErrorStatistics === 'function';
        } catch {
          return false;
        }
      }
    },
    {
      name: 'All Services Initialized',
      description: 'Verify all services are properly initialized',
      critical: true,
      test: () => {
        try {
          const services = (window as any).__GLOBAL_SERVICES__;
          return services?.allInitialized === true;
        } catch {
          return false;
        }
      }
    },
    {
      name: 'Service Health Monitoring',
      description: 'Verify service health monitoring is functional',
      critical: false,
      test: () => {
        try {
          const services = (window as any).__GLOBAL_SERVICES__;
          const health = services?.serviceHealth;
          return !!health && typeof health.status === 'string' && typeof health.healthPercentage === 'number';
        } catch {
          return false;
        }
      }
    },
    {
      name: 'Cache Service Functionality',
      description: 'Test cache service basic operations',
      critical: false,
      test: () => {
        try {
          const services = (window as any).__GLOBAL_SERVICES__;
          const cache = services?.cacheService;
          if (!cache) return false;
          
          // Test basic cache operations
          cache.set('test-key', 'test-value');
          const value = cache.get('test-key');
          cache.invalidate('test-key');
          
          return value === 'test-value';
        } catch {
          return false;
        }
      }
    },
    {
      name: 'AI Services Accessibility',
      description: 'Verify AI services are accessible and functional',
      critical: false,
      test: () => {
        try {
          const services = (window as any).__GLOBAL_SERVICES__;
          const aiService = services?.aiChatTriggerService;
          const aiEnhanced = services?.aiEnhancedService;
          
          return !!aiService && !!aiEnhanced && 
                 typeof aiService.triggerChat === 'function' &&
                 typeof aiEnhanced.sendEnhancedChatMessage === 'function';
        } catch {
          return false;
        }
      }
    },
    {
      name: 'Profile Services Integration',
      description: 'Verify profile services are properly integrated',
      critical: false,
      test: () => {
        try {
          const services = (window as any).__GLOBAL_SERVICES__;
          const profileService = services?.profileService;
          const profileManager = services?.profileManager;
          const profileCompletion = services?.profileCompletionService;
          
          return !!profileService && !!profileManager && !!profileCompletion &&
                 typeof profileService.formatProfileType === 'function' &&
                 typeof profileManager.invalidateProfile === 'function';
        } catch {
          return false;
        }
      }
    },
    {
      name: 'Development Tools (Dev Only)',
      description: 'Verify development tools are available in development',
      critical: false,
      test: () => {
        const isDev = import.meta.env.MODE === 'development';
        if (!isDev) return true; // Pass in non-dev environments
        
        try {
          const devTools = (window as any).__SERVICE_DEV_TOOLS__;
          const diagnostics = (window as any).__SERVICE_DIAGNOSTICS__;
          
          return !!devTools && typeof diagnostics === 'function';
        } catch {
          return false;
        }
      }
    },
    {
      name: 'Error Recovery Mechanism',
      description: 'Test error recovery functionality',
      critical: false,
      test: async () => {
        try {
          const services = (window as any).__GLOBAL_SERVICES__;
          if (!services || typeof services.recoverFailedServices !== 'function') {
            return false;
          }
          
          // Test recovery function exists and is callable
          await services.recoverFailedServices();
          return true;
        } catch {
          return false;
        }
      }
    },
    {
      name: 'Service Dependency Validation',
      description: 'Verify service dependencies are properly managed',
      critical: false,
      test: () => {
        try {
          const services = (window as any).__GLOBAL_SERVICES__;
          if (!services || typeof services.validateServiceDependencies !== 'function') {
            return false;
          }
          
          const validation = services.validateServiceDependencies();
          return validation.valid === true;
        } catch {
          return false;
        }
      }
    },
    {
      name: 'Performance Metrics Collection',
      description: 'Verify performance metrics are being collected',
      critical: false,
      test: () => {
        try {
          const devTools = (window as any).__SERVICE_DEV_TOOLS__;
          if (!devTools || typeof devTools.getPerformanceMetrics !== 'function') {
            return import.meta.env.MODE !== 'development'; // Pass in non-dev
          }
          
          const metrics = devTools.getPerformanceMetrics();
          return !!metrics && typeof metrics.serviceInitializationTime === 'number';
        } catch {
          return false;
        }
      }
    }
  ];

  async runValidation(): Promise<IntegrationValidationReport> {
    console.log('🔍 Starting Main App Integration Validation...');
    
    const results: ValidationResult[] = [];
    const startTime = Date.now();

    for (const test of this.tests) {
      const testStartTime = Date.now();
      
      try {
        console.log(`  Testing: ${test.name}...`);
        
        const passed = await test.test();
        const duration = Date.now() - testStartTime;
        
        results.push({
          testName: test.name,
          passed,
          duration,
          critical: test.critical
        });
        
        console.log(`  ${passed ? '✅' : '❌'} ${test.name} - ${passed ? 'PASSED' : 'FAILED'} (${duration}ms)`);
        
      } catch (error: any) {
        const duration = Date.now() - testStartTime;
        
        results.push({
          testName: test.name,
          passed: false,
          error: error.message,
          duration,
          critical: test.critical
        });
        
        console.log(`  ❌ ${test.name} - FAILED: ${error.message} (${duration}ms)`);
      }
    }

    const totalTests = results.length;
    const passedTests = results.filter(r => r.passed).length;
    const failedTests = totalTests - passedTests;
    const criticalFailures = results.filter(r => !r.passed && r.critical).length;

    const overallStatus: 'PASSED' | 'FAILED' | 'PARTIAL' = 
      criticalFailures > 0 ? 'FAILED' :
      failedTests === 0 ? 'PASSED' : 'PARTIAL';

    const environment = import.meta.env.MODE || 'unknown';
    const totalDuration = Date.now() - startTime;

    const report: IntegrationValidationReport = {
      overallStatus,
      environment,
      timestamp: new Date(),
      totalTests,
      passedTests,
      failedTests,
      criticalFailures,
      results,
      summary: `Integration validation ${overallStatus.toLowerCase()}: ${passedTests}/${totalTests} tests passed (${totalDuration}ms)`,
      recommendations: this.generateRecommendations(results)
    };

    console.log(`\n📊 Validation Complete: ${report.summary}`);
    
    if (report.recommendations.length > 0) {
      console.log('\n💡 Recommendations:');
      report.recommendations.forEach(rec => console.log(`  - ${rec}`));
    }

    return report;
  }

  private generateRecommendations(results: ValidationResult[]): string[] {
    const recommendations: string[] = [];
    const failedTests = results.filter(r => !r.passed);

    if (failedTests.length === 0) {
      recommendations.push('All tests passed! The main app integration is working correctly.');
      return recommendations;
    }

    // Critical failure recommendations
    const criticalFailures = failedTests.filter(r => r.critical);
    if (criticalFailures.length > 0) {
      recommendations.push('CRITICAL: Fix critical failures before proceeding to production');
      criticalFailures.forEach(test => {
        recommendations.push(`  - Fix ${test.testName}: ${test.error || 'Unknown error'}`);
      });
    }

    // Specific recommendations based on failed tests
    failedTests.forEach(test => {
      switch (test.testName) {
        case 'Pinia Store Initialization':
          recommendations.push('Ensure Pinia is properly installed and initialized before other plugins');
          break;
        case 'Service Coordination Plugin':
          recommendations.push('Check service coordination plugin installation and configuration');
          break;
        case 'Global Error Handler':
          recommendations.push('Verify global error handler is installed early in the app setup');
          break;
        case 'All Services Initialized':
          recommendations.push('Check service initialization logs for specific service failures');
          break;
        case 'Cache Service Functionality':
          recommendations.push('Verify cache service implementation and dependencies');
          break;
        case 'AI Services Accessibility':
          recommendations.push('Check AI service initialization and API configuration');
          break;
        case 'Profile Services Integration':
          recommendations.push('Verify profile service dependencies and initialization order');
          break;
        case 'Development Tools (Dev Only)':
          recommendations.push('Check development tools initialization in development environment');
          break;
        case 'Error Recovery Mechanism':
          recommendations.push('Test error recovery functionality and service coordination');
          break;
        case 'Service Dependency Validation':
          recommendations.push('Review service dependency configuration and initialization order');
          break;
      }
    });

    return recommendations;
  }
}

/**
 * Quick validation function for console use
 */
export async function validateMainAppIntegration(): Promise<IntegrationValidationReport> {
  const validator = new MainAppIntegrationValidator();
  return await validator.runValidation();
}

// Make available in browser console
if (typeof window !== 'undefined') {
  (window as any).__validateMainAppIntegration = validateMainAppIntegration;
  console.log('🔧 Main app validation available: Use __validateMainAppIntegration() in console');
}
